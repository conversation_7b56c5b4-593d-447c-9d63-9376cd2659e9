<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\OfflineShipping\Model\Carrier\Freeshipping;
use Magento\Quote\Model\Quote\Address\RateRequestFactory;

class TestFreeShippingCarrier extends Command
{
    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly Freeshipping $freeShippingCarrier,
        private readonly RateRequestFactory $rateRequestFactory
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setName('coditron:test-freeshipping-carrier')
            ->setDescription('Test if the core free shipping carrier is working');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Testing Core Free Shipping Carrier</info>');
        $output->writeln('');

        // Check configuration
        $config = [
            'active' => $this->scopeConfig->getValue('carriers/freeshipping/active', ScopeInterface::SCOPE_STORE),
            'title' => $this->scopeConfig->getValue('carriers/freeshipping/title', ScopeInterface::SCOPE_STORE),
            'name' => $this->scopeConfig->getValue('carriers/freeshipping/name', ScopeInterface::SCOPE_STORE),
            'free_shipping_subtotal' => $this->scopeConfig->getValue('carriers/freeshipping/free_shipping_subtotal', ScopeInterface::SCOPE_STORE),
            'sallowspecific' => $this->scopeConfig->getValue('carriers/freeshipping/sallowspecific', ScopeInterface::SCOPE_STORE),
            'specificcountry' => $this->scopeConfig->getValue('carriers/freeshipping/specificcountry', ScopeInterface::SCOPE_STORE),
        ];

        $output->writeln('<info>Free Shipping Configuration:</info>');
        foreach ($config as $key => $value) {
            $output->writeln("  {$key}: " . ($value ?? 'NULL'));
        }
        $output->writeln('');

        // Test if carrier is enabled
        $isEnabled = $this->freeShippingCarrier->getConfigFlag('active');
        $output->writeln("Carrier Enabled: " . ($isEnabled ? 'YES' : 'NO'));
        
        if (!$isEnabled) {
            $output->writeln('<error>Free shipping carrier is DISABLED. Enable it in admin first.</error>');
            return Command::FAILURE;
        }

        // Test rate collection
        $rateRequest = $this->rateRequestFactory->create();
        $rateRequest->setDestCountryId('IT');
        $rateRequest->setBaseSubtotalInclTax(100.00);
        $rateRequest->setPackageWeight(1);
        $rateRequest->setPackageValue(100.00);

        $output->writeln('<info>Testing Rate Collection:</info>');
        $output->writeln("Country: IT");
        $output->writeln("Subtotal: 100.00");
        $output->writeln('');

        // Temporarily enable free shipping for testing
        $originalConfig = $this->freeShippingCarrier->getConfigData();
        $testConfig = $originalConfig;
        $testConfig['active'] = 1;
        $testConfig['free_shipping_subtotal'] = 0; // No minimum

        // Use reflection to set config for testing
        $reflection = new \ReflectionClass($this->freeShippingCarrier);
        if ($reflection->hasProperty('_configData')) {
            $configProperty = $reflection->getProperty('_configData');
            $configProperty->setAccessible(true);
            $configProperty->setValue($this->freeShippingCarrier, $testConfig);
        }

        $result = $this->freeShippingCarrier->collectRates($rateRequest);

        if ($result === false) {
            $output->writeln('<error>Free shipping returned FALSE</error>');
        } elseif ($result && $result->getAllRates()) {
            $rates = $result->getAllRates();
            $output->writeln('<info>SUCCESS: Free shipping rates found: ' . count($rates) . '</info>');
            foreach ($rates as $rate) {
                $output->writeln("  - {$rate->getCarrier()}: {$rate->getMethodTitle()} (${$rate->getPrice()})");
            }
        } else {
            $output->writeln('<comment>Free shipping result exists but no rates found</comment>');
            $output->writeln('Result type: ' . ($result ? get_class($result) : 'NULL'));
        }

        return Command::SUCCESS;
    }
}
