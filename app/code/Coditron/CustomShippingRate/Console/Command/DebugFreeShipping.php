<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Coditron\CustomShippingRate\Service\FreeShippingService;
use Magento\Quote\Model\Quote\Address\RateRequest;
use Magento\Quote\Model\Quote\Address\RateRequestFactory;
use Magento\OfflineShipping\Model\Carrier\Freeshipping;

class DebugFreeShipping extends Command
{
    private const COUNTRY_OPTION = 'country';
    private const SUBTOTAL_OPTION = 'subtotal';

    public function __construct(
        private readonly FreeShippingService $freeShippingService,
        private readonly RateRequestFactory $rateRequestFactory,
        private readonly Freeshipping $freeShippingCarrier
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setName('coditron:debug-freeshipping')
            ->setDescription('Debug free shipping configuration and thresholds')
            ->addOption(
                self::COUNTRY_OPTION,
                'c',
                InputOption::VALUE_OPTIONAL,
                'Country code to test (e.g., US, GB)',
                'US'
            )
            ->addOption(
                self::SUBTOTAL_OPTION,
                's',
                InputOption::VALUE_OPTIONAL,
                'Subtotal amount to test',
                '100.00'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $country = $input->getOption(self::COUNTRY_OPTION);
        $subtotal = (float)$input->getOption(self::SUBTOTAL_OPTION);

        $output->writeln('<info>Debugging Free Shipping Configuration</info>');
        $output->writeln("Country: {$country}");
        $output->writeln("Subtotal: {$subtotal}");
        $output->writeln('');

        // Log Magento configuration
        $this->freeShippingService->logFreeShippingConfiguration();
        
        // Log all table rates
        $this->freeShippingService->logAllFreeShippingTableRates();

        // Check for thresholds
        $hasThresholds = $this->freeShippingService->hasThresholdsForCountry($country);
        $metThresholds = $this->freeShippingService->getMetThresholdsForCountry($country, $subtotal);

        $output->writeln("Has thresholds for {$country}: " . ($hasThresholds ? 'YES' : 'NO'));
        $output->writeln("Met thresholds count: " . count($metThresholds));

        // Test rate collection
        $rateRequest = $this->rateRequestFactory->create();
        $rateRequest->setDestCountryId($country);
        $rateRequest->setBaseSubtotalInclTax($subtotal);
        $rateRequest->setPackageWeight(1);
        $rateRequest->setPackageValue($subtotal);

        $output->writeln('');
        $output->writeln('<info>Testing Free Shipping Rate Collection</info>');
        
        $result = $this->freeShippingCarrier->collectRates($rateRequest);
        
        if ($result === false) {
            $output->writeln('<error>Free shipping returned FALSE - no rates available</error>');
        } elseif ($result && $result->getAllRates()) {
            $rates = $result->getAllRates();
            $output->writeln('<info>Free shipping rates found: ' . count($rates) . '</info>');
            foreach ($rates as $rate) {
                $output->writeln("  - {$rate->getCarrier()}: {$rate->getMethodTitle()} (${$rate->getPrice()})");
            }
        } else {
            $output->writeln('<comment>Free shipping result exists but no rates found</comment>');
        }

        $output->writeln('');
        $output->writeln('<info>Check var/log/system.log for detailed logging information</info>');

        return Command::SUCCESS;
    }
}
