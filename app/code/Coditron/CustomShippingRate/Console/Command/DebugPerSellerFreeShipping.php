<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Coditron\CustomShippingRate\Service\FreeShippingService;

class DebugPerSellerFreeShipping extends Command
{
    private const COUNTRY_OPTION = 'country';
    private const SELLERS_OPTION = 'sellers';

    public function __construct(
        private readonly FreeShippingService $freeShippingService
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setName('coditron:debug-per-seller-freeshipping')
            ->setDescription('Debug per-seller free shipping thresholds')
            ->addOption(
                self::COUNTRY_OPTION,
                'c',
                InputOption::VALUE_OPTIONAL,
                'Country code to test (e.g., US, GB, IT)',
                'IT'
            )
            ->addOption(
                self::SELLERS_OPTION,
                's',
                InputOption::VALUE_OPTIONAL,
                'Seller subtotals in format "seller1:amount1,seller2:amount2"',
                '67439:50,67440:100'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $country = $input->getOption(self::COUNTRY_OPTION);
        $sellersInput = $input->getOption(self::SELLERS_OPTION);

        $output->writeln('<info>Debugging Per-Seller Free Shipping</info>');
        $output->writeln("Country: {$country}");
        $output->writeln("Sellers Input: {$sellersInput}");
        $output->writeln('');

        // Parse seller subtotals
        $sellerSubtotals = [];
        $sellerPairs = explode(',', $sellersInput);
        foreach ($sellerPairs as $pair) {
            [$sellerId, $amount] = explode(':', $pair);
            $sellerSubtotals[trim($sellerId)] = (float)trim($amount);
        }

        $output->writeln('<info>Parsed Seller Subtotals:</info>');
        foreach ($sellerSubtotals as $sellerId => $subtotal) {
            $output->writeln("  Seller {$sellerId}: €{$subtotal}");
        }
        $output->writeln('');

        // Log all free shipping table rates
        $this->freeShippingService->logAllFreeShippingTableRates();

        // Check which sellers have thresholds
        $sellersWithThresholds = $this->freeShippingService->getSellersWithThresholds($country, array_keys($sellerSubtotals));
        
        $output->writeln('<info>Sellers with Thresholds:</info>');
        foreach ($sellersWithThresholds as $sellerId => $hasThresholds) {
            $status = $hasThresholds ? 'YES' : 'NO';
            $output->writeln("  Seller {$sellerId}: {$status}");
        }
        $output->writeln('');

        // Check per-seller threshold evaluation
        $qualifyingSellers = $this->freeShippingService->getMetThresholdsPerSeller($country, $sellerSubtotals);

        $output->writeln('<info>Per-Seller Threshold Evaluation:</info>');
        foreach ($sellerSubtotals as $sellerId => $subtotal) {
            if (isset($qualifyingSellers[$sellerId])) {
                $thresholds = $qualifyingSellers[$sellerId];
                $lowestThreshold = min(array_map(function($threshold) {
                    return $threshold->getMinOrderAmount();
                }, $thresholds));
                
                $output->writeln("  <info>✅ Seller {$sellerId}: QUALIFIES (€{$subtotal} >= €{$lowestThreshold})</info>");
                $output->writeln("    Thresholds met: " . count($thresholds));
                foreach ($thresholds as $threshold) {
                    $output->writeln("      - ID: {$threshold->getId()}, Min: €{$threshold->getMinOrderAmount()}, Service: {$threshold->getServiceType()}");
                }
            } else {
                $hasThresholds = $sellersWithThresholds[$sellerId] ?? false;
                if ($hasThresholds) {
                    $output->writeln("  <comment>❌ Seller {$sellerId}: HAS THRESHOLDS but none met (€{$subtotal})</comment>");
                } else {
                    $output->writeln("  <comment>⚪ Seller {$sellerId}: NO THRESHOLDS defined</comment>");
                }
            }
        }
        $output->writeln('');

        $output->writeln('<info>Summary:</info>');
        $output->writeln("Total sellers: " . count($sellerSubtotals));
        $output->writeln("Qualifying sellers: " . count($qualifyingSellers));
        $output->writeln("Qualifying seller IDs: " . implode(', ', array_keys($qualifyingSellers)));

        $output->writeln('');
        $output->writeln('<info>Check var/log/system.log for detailed logging information</info>');

        return Command::SUCCESS;
    }
}
