<?php
/**
 * Copyright © Coditron Technologies All rights reserved.
 * See COPYING.txt for license details.
 * http://www.coditron.com | <EMAIL>
 */

namespace Coditron\CustomShippingRate\Plugin\Shipping\Model\Carrier;

use Magento\OfflineShipping\Model\Carrier\Freeshipping;
use Magento\Quote\Model\Quote\Address\RateRequest;
use Coditron\CustomShippingRate\Service\FreeShippingService;

/**
 * Plugin to conditionally enable core freeshipping when thresholds exist and are met
 */
class FreeShippingPlugin
{
    public function __construct(
        private readonly FreeShippingService $freeShippingService
    ) {
    }

    /**
     * Enhance core freeshipping with custom thresholds when they exist and are met
     *
     * @param Freeshipping $subject
     * @param callable $proceed
     * @param RateRequest $request
     * @return \Magento\Shipping\Model\Rate\Result|bool
     */
    public function aroundCollectRates(Freeshipping $subject, callable $proceed, RateRequest $request)
    {
        $country = $request->getDestCountryId();
        $subtotal = $request->getBaseSubtotalInclTax();

        $metThresholds = $this->freeShippingService->getMetThresholdsForCountry($country, $subtotal);

        // If custom thresholds exist and are met, override the configuration
        if (!empty($metThresholds)) {
            $this->freeShippingService->overrideFreeShippingConfig($subject, $metThresholds);
        }

        // Always proceed with the original logic (let Magento handle default free shipping)
        $result = $proceed($request);

        // Add custom threshold subtitle only if custom thresholds were met
        if ($result && $result->getAllRates() && !empty($metThresholds)) {
            $this->freeShippingService->addThresholdSubtitle($result, $metThresholds);
        }

        return $result;
    }
}
