<?php
/**
 * Copyright © Coditron Technologies All rights reserved.
 * See COPYING.txt for license details.
 * http://www.coditron.com | <EMAIL>
 */

namespace Coditron\CustomShippingRate\Plugin\Shipping\Model\Carrier;

use Magento\OfflineShipping\Model\Carrier\Freeshipping;
use Magento\Quote\Model\Quote\Address\RateRequest;
use Coditron\CustomShippingRate\Service\FreeShippingService;
use Psr\Log\LoggerInterface;

/**
 * Plugin to conditionally enable core freeshipping when thresholds exist and are met
 */
class FreeShippingPlugin
{
    public function __construct(
        private readonly FreeShippingService $freeShippingService,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Enhance core freeshipping with custom thresholds when they exist and are met
     *
     * @param Freeshipping $subject
     * @param callable $proceed
     * @param RateRequest $request
     * @return \Magento\Shipping\Model\Rate\Result|bool
     */
    public function aroundCollectRates(Freeshipping $subject, callable $proceed, RateRequest $request)
    {
        $country = $request->getDestCountryId();
        $subtotal = $request->getBaseSubtotalInclTax();

        $this->logger->info('[FreeShippingPlugin] Starting free shipping rate collection', [
            'country' => $country,
            'subtotal' => $subtotal,
            'dest_city' => $request->getDestCity(),
            'dest_postcode' => $request->getDestPostcode(),
            'package_weight' => $request->getPackageWeight(),
            'package_value' => $request->getPackageValue(),
            'free_shipping' => $request->getFreeShipping()
        ]);

        // Check if the carrier is enabled and log configuration
        $isEnabled = $subject->getConfigFlag('active');
        $this->logger->info('[FreeShippingPlugin] Free shipping carrier status', [
            'is_enabled' => $isEnabled,
            'carrier_code' => $subject->getCarrierCode(),
            'config_data' => $subject->getConfigData()
        ]);

        // Log the complete Magento free shipping configuration
        $this->freeShippingService->logFreeShippingConfiguration();

        // Log all free shipping table rates for debugging
        $this->freeShippingService->logAllFreeShippingTableRates();

        $metThresholds = $this->freeShippingService->getMetThresholdsForCountry($country, $subtotal);

        $this->logger->info('[FreeShippingPlugin] Custom thresholds check', [
            'thresholds_found' => count($metThresholds),
            'threshold_details' => array_map(function($threshold) {
                return [
                    'id' => $threshold->getId(),
                    'min_amount' => $threshold->getMinOrderAmount(),
                    'countries' => $threshold->getCountries(),
                    'seller_id' => $threshold->getSellerId()
                ];
            }, $metThresholds)
        ]);

        // If custom thresholds exist and are met, override the configuration
        if (!empty($metThresholds)) {
            $this->logger->info('[FreeShippingPlugin] Overriding free shipping configuration with custom thresholds');
            $this->freeShippingService->overrideFreeShippingConfig($subject, $metThresholds);
        }

        // Always proceed with the original logic (let Magento handle default free shipping)
        $this->logger->info('[FreeShippingPlugin] Proceeding with original free shipping logic');
        $result = $proceed($request);

        // Log the result
        if ($result === false) {
            $this->logger->warning('[FreeShippingPlugin] Free shipping returned false - no rates available');
        } elseif ($result && $result->getAllRates()) {
            $rates = $result->getAllRates();
            $this->logger->info('[FreeShippingPlugin] Free shipping rates collected', [
                'rate_count' => count($rates),
                'rates' => array_map(function($rate) {
                    return [
                        'carrier' => $rate->getCarrier(),
                        'method' => $rate->getMethod(),
                        'method_title' => $rate->getMethodTitle(),
                        'price' => $rate->getPrice(),
                        'cost' => $rate->getCost()
                    ];
                }, $rates)
            ]);

            // Add custom threshold subtitle only if custom thresholds were met
            if (!empty($metThresholds)) {
                $this->logger->info('[FreeShippingPlugin] Adding custom threshold subtitles');
                $this->freeShippingService->addThresholdSubtitle($result, $metThresholds);
            }
        } else {
            $this->logger->info('[FreeShippingPlugin] Free shipping result exists but no rates found', [
                'result_type' => get_class($result),
                'result_data' => $result ? $result->getData() : null
            ]);
        }

        $this->logger->info('[FreeShippingPlugin] Completed free shipping rate collection');
        return $result;
    }
}
