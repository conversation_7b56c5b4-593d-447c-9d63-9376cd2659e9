<?php
/**
 * Copyright © Coditron Technologies All rights reserved.
 * See COPYING.txt for license details.
 * http://www.coditron.com | <EMAIL>
 */

namespace Coditron\CustomShippingRate\Plugin\Shipping\Model\Carrier;

use Magento\OfflineShipping\Model\Carrier\Freeshipping;
use Magento\Quote\Model\Quote\Address\RateRequest;
use Coditron\CustomShippingRate\Service\FreeShippingService;

/**
 * Plugin to conditionally enable core freeshipping when thresholds exist and are met
 */
class FreeShippingPlugin
{
    public function __construct(
        private readonly FreeShippingService $freeShippingService
    ) {
    }

    /**
     * Enable core freeshipping only when thresholds exist and are met for the country
     *
     * @param Freeshipping $subject
     * @param callable $proceed
     * @param RateRequest $request
     * @return \Magento\Shipping\Model\Rate\Result|bool
     */
    public function aroundCollectRates(Freeshipping $subject, callable $proceed, RateRequest $request)
    {
        $country = $request->getDestCountryId();
        $subtotal = $request->getBaseSubtotalInclTax();

        $metThresholds = $this->freeShippingService->getMetThresholdsForCountry($country, $subtotal);

        if (empty($metThresholds)) {
            return false;
        }

        $this->freeShippingService->overrideFreeShippingConfig($subject, $metThresholds);

        $result = $proceed($request);

        if ($result && $result->getAllRates()) {
            $this->freeShippingService->addThresholdSubtitle($result, $metThresholds);
        }

        return $result;
    }
}
