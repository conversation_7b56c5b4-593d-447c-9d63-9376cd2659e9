<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Plugin\Model;

use Coditron\CustomShippingRate\Model\Carrier;
use Coditron\CustomShippingRate\Service\FreeShippingService;
use Magento\Quote\Model\Quote\Address\RateRequest;
use Magento\Quote\Model\Quote\Address\RateResult\MethodFactory;
use Magento\Shipping\Model\Rate\Result;
use Psr\Log\LoggerInterface;
use Comave\SplitOrder\Api\SellerCartDetailsInterface;

class CarrierPlugin
{
    public function __construct(
        private readonly FreeShippingService $freeShippingService,
        private readonly LoggerInterface $logger,
        private readonly MethodFactory $rateMethodFactory
    ) {
    }

    /**
     * Add per-seller free shipping options to the custom shipping rates
     *
     * @param Carrier $subject
     * @param Result $result
     * @param RateRequest $request
     * @return Result
     */
    public function afterCollectRates(Carrier $subject, $result, RateRequest $request): Result
    {
        $this->logger->info('[CarrierPlugin] Plugin triggered - checking if we should evaluate per-seller free shipping', [
            'has_result' => $result !== null,
            'result_type' => $result ? get_class($result) : 'null',
            'has_items' => $request->getAllItems() ? count($request->getAllItems()) : 0
        ]);

        if (!$result || !$request->getAllItems()) {
            $this->logger->info('[CarrierPlugin] Skipping - no result or no items');
            return $result;
        }

        $this->logger->info('[CarrierPlugin] Starting per-seller free shipping evaluation');

        try {
            // Get quote and seller data
            $quote = current($request->getAllItems())->getQuote();
            $country = $request->getDestCountryId();

            $this->logger->info('[CarrierPlugin] Basic request info', [
                'quote_id' => $quote->getId(),
                'country' => $country,
                'quote_items_count' => count($quote->getAllVisibleItems()),
                'request_subtotal' => $request->getBaseSubtotalInclTax()
            ]);

            // Calculate seller subtotals from quote items
            $sellerSubtotals = $this->calculateSellerSubtotals($quote->getAllVisibleItems());

            $this->logger->info('[CarrierPlugin] Calculated seller subtotals', [
                'country' => $country,
                'seller_subtotals' => $sellerSubtotals,
                'total_sellers' => count($sellerSubtotals)
            ]);

            if (empty($sellerSubtotals)) {
                $this->logger->warning('[CarrierPlugin] No seller subtotals found - skipping free shipping evaluation');
                return $result;
            }

            // Check which sellers have thresholds defined
            $sellersWithThresholds = $this->freeShippingService->getSellersWithThresholds($country, array_keys($sellerSubtotals));

            $this->logger->info('[CarrierPlugin] Sellers with thresholds check', [
                'sellers_with_thresholds' => $sellersWithThresholds
            ]);

            // Get per-seller threshold evaluation
            $qualifyingSellers = $this->freeShippingService->getMetThresholdsPerSeller($country, $sellerSubtotals);

            $this->logger->info('[CarrierPlugin] Per-seller threshold evaluation results', [
                'qualifying_sellers' => array_keys($qualifyingSellers),
                'qualifying_count' => count($qualifyingSellers),
                'threshold_details' => array_map(function($thresholds) {
                    return array_map(function($threshold) {
                        return [
                            'id' => $threshold->getId(),
                            'min_amount' => $threshold->getMinOrderAmount(),
                            'service_type' => $threshold->getServiceType()
                        ];
                    }, $thresholds);
                }, $qualifyingSellers)
            ]);

            if (empty($qualifyingSellers)) {
                $this->logger->warning('[CarrierPlugin] No sellers qualify for free shipping');
                return $result;
            }

            // Log current result before adding free shipping
            $currentRates = $result->getAllRates();
            $this->logger->info('[CarrierPlugin] Current rates before adding free shipping', [
                'current_rate_count' => count($currentRates),
                'current_rates' => array_map(function($rate) {
                    return [
                        'carrier' => $rate->getCarrier(),
                        'method' => $rate->getMethod(),
                        'title' => $rate->getMethodTitle(),
                        'price' => $rate->getPrice()
                    ];
                }, $currentRates)
            ]);

            // Add free shipping rates for qualifying sellers
            $this->addPerSellerFreeShippingRates($result, $qualifyingSellers, $subject);

            // Log final result
            $finalRates = $result->getAllRates();
            $this->logger->info('[CarrierPlugin] Final rates after adding free shipping', [
                'final_rate_count' => count($finalRates),
                'added_rates' => count($finalRates) - count($currentRates),
                'final_rates' => array_map(function($rate) {
                    return [
                        'carrier' => $rate->getCarrier(),
                        'method' => $rate->getMethod(),
                        'title' => $rate->getMethodTitle(),
                        'price' => $rate->getPrice()
                    ];
                }, $finalRates)
            ]);

        } catch (\Exception $e) {
            $this->logger->error('[CarrierPlugin] Error during per-seller free shipping evaluation', [
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString()
            ]);
        }

        return $result;
    }

    /**
     * Calculate subtotals per seller from quote items
     *
     * @param array $quoteItems
     * @return array ['seller_id' => subtotal]
     */
    private function calculateSellerSubtotals(array $quoteItems): array
    {
        $this->logger->info('[CarrierPlugin] Starting seller subtotal calculation', [
            'total_items' => count($quoteItems)
        ]);

        $sellerSubtotals = [];

        foreach ($quoteItems as $item) {
            $this->logger->info('[CarrierPlugin] Processing quote item', [
                'item_id' => $item->getId(),
                'product_id' => $item->getProductId(),
                'name' => $item->getName(),
                'qty' => $item->getQty(),
                'row_total' => $item->getRowTotal(),
                'available_options' => array_keys($item->getOptions())
            ]);

            $itemSellerData = $item->getOptionByCode('option_' . SellerCartDetailsInterface::SELLER_OPTION);

            if (empty($itemSellerData)) {
                $this->logger->info('[CarrierPlugin] No seller option found for item', [
                    'item_id' => $item->getId(),
                    'expected_option_code' => 'option_' . SellerCartDetailsInterface::SELLER_OPTION
                ]);
                continue;
            }

            $sellerDataValue = $itemSellerData->getValue() ?? '{}';
            $sellerData = json_decode($sellerDataValue, true);

            $this->logger->info('[CarrierPlugin] Found seller data for item', [
                'item_id' => $item->getId(),
                'seller_data_raw' => $sellerDataValue,
                'seller_data_parsed' => $sellerData
            ]);

            $sellerId = $sellerData['seller_id'] ?? null;

            if (!$sellerId) {
                $this->logger->warning('[CarrierPlugin] No seller_id found in seller data', [
                    'item_id' => $item->getId(),
                    'seller_data' => $sellerData
                ]);
                continue;
            }

            if (!isset($sellerSubtotals[$sellerId])) {
                $sellerSubtotals[$sellerId] = 0;
            }

            $sellerSubtotals[$sellerId] += $item->getRowTotal();

            $this->logger->info('[CarrierPlugin] Added item to seller subtotal', [
                'item_id' => $item->getId(),
                'seller_id' => $sellerId,
                'item_row_total' => $item->getRowTotal(),
                'seller_new_subtotal' => $sellerSubtotals[$sellerId]
            ]);
        }

        $this->logger->info('[CarrierPlugin] Completed seller subtotal calculation', [
            'final_seller_subtotals' => $sellerSubtotals
        ]);

        return $sellerSubtotals;
    }

    /**
     * Add free shipping rates for qualifying sellers
     *
     * @param Result $result
     * @param array $qualifyingSellers
     * @param Carrier $carrier
     * @return void
     */
    private function addPerSellerFreeShippingRates(Result $result, array $qualifyingSellers, Carrier $carrier): void
    {
        $this->logger->info('[CarrierPlugin] Starting to add free shipping rates', [
            'qualifying_sellers_count' => count($qualifyingSellers),
            'carrier_code' => $carrier->getCarrierCode(),
            'carrier_title' => $carrier->getConfigData('title')
        ]);

        foreach ($qualifyingSellers as $sellerId => $thresholds) {
            $this->logger->info('[CarrierPlugin] Processing seller for free shipping rate', [
                'seller_id' => $sellerId,
                'thresholds_count' => count($thresholds)
            ]);

            // Get the lowest threshold for subtitle
            $lowestThreshold = min(array_map(function($threshold) {
                return $threshold->getMinOrderAmount();
            }, $thresholds));

            $this->logger->info('[CarrierPlugin] Calculated lowest threshold', [
                'seller_id' => $sellerId,
                'lowest_threshold' => $lowestThreshold
            ]);

            try {
                // Create free shipping rate for this seller
                $rate = $this->rateMethodFactory->create();
                $methodCode = 'freeshipping_seller_' . $sellerId;
                $methodTitle = sprintf('Free Shipping - Seller %s (Orders over €%.2f)', $sellerId, $lowestThreshold);

                $rate->setCarrier($carrier->getCarrierCode());
                $rate->setCarrierTitle($carrier->getConfigData('title'));
                $rate->setMethod($methodCode);
                $rate->setMethodTitle($methodTitle);
                $rate->setPrice(0);
                $rate->setCost(0);

                $this->logger->info('[CarrierPlugin] Created free shipping rate', [
                    'seller_id' => $sellerId,
                    'carrier' => $carrier->getCarrierCode(),
                    'carrier_title' => $carrier->getConfigData('title'),
                    'method_code' => $methodCode,
                    'method_title' => $methodTitle,
                    'price' => 0,
                    'cost' => 0
                ]);

                $result->append($rate);

                $this->logger->info('[CarrierPlugin] Successfully added free shipping rate for seller', [
                    'seller_id' => $sellerId,
                    'threshold' => $lowestThreshold,
                    'method_code' => $methodCode,
                    'total_rates_now' => count($result->getAllRates())
                ]);

            } catch (\Exception $e) {
                $this->logger->error('[CarrierPlugin] Error creating free shipping rate for seller', [
                    'seller_id' => $sellerId,
                    'error_message' => $e->getMessage(),
                    'error_trace' => $e->getTraceAsString()
                ]);
            }
        }

        $this->logger->info('[CarrierPlugin] Completed adding free shipping rates', [
            'total_rates_added' => count($qualifyingSellers),
            'final_rate_count' => count($result->getAllRates())
        ]);
    }
}
