<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Plugin\Model;

use Coditron\CustomShippingRate\Model\Carrier;
use Coditron\CustomShippingRate\Service\FreeShippingService;
use Magento\Quote\Model\Quote\Address\RateRequest;
use Magento\Quote\Model\Quote\Address\RateResult\MethodFactory;
use Magento\Shipping\Model\Rate\Result;
use Psr\Log\LoggerInterface;
use Comave\SplitOrder\Api\SellerCartDetailsInterface;

class CarrierPlugin
{
    public function __construct(
        private readonly FreeShippingService $freeShippingService,
        private readonly LoggerInterface $logger,
        private readonly MethodFactory $rateMethodFactory
    ) {
    }

    /**
     * Add per-seller free shipping options to the custom shipping rates
     *
     * @param Carrier $subject
     * @param Result $result
     * @param RateRequest $request
     * @return Result
     */
    public function afterCollectRates(Carrier $subject, $result, RateRequest $request): Result
    {
        if (!$result || !$request->getAllItems()) {
            return $result;
        }

        $this->logger->info('[CarrierPlugin] Starting per-seller free shipping evaluation');

        try {
            // Get quote and seller data
            $quote = current($request->getAllItems())->getQuote();
            $country = $request->getDestCountryId();
            
            // Calculate seller subtotals from quote items
            $sellerSubtotals = $this->calculateSellerSubtotals($quote->getAllVisibleItems());
            
            $this->logger->info('[CarrierPlugin] Calculated seller subtotals', [
                'country' => $country,
                'seller_subtotals' => $sellerSubtotals
            ]);

            if (empty($sellerSubtotals)) {
                $this->logger->info('[CarrierPlugin] No seller subtotals found - skipping free shipping evaluation');
                return $result;
            }

            // Get per-seller threshold evaluation
            $qualifyingSellers = $this->freeShippingService->getMetThresholdsPerSeller($country, $sellerSubtotals);
            
            if (empty($qualifyingSellers)) {
                $this->logger->info('[CarrierPlugin] No sellers qualify for free shipping');
                return $result;
            }

            // Add free shipping rates for qualifying sellers
            $this->addPerSellerFreeShippingRates($result, $qualifyingSellers, $subject);

        } catch (\Exception $e) {
            $this->logger->error('[CarrierPlugin] Error during per-seller free shipping evaluation', [
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString()
            ]);
        }

        return $result;
    }

    /**
     * Calculate subtotals per seller from quote items
     *
     * @param array $quoteItems
     * @return array ['seller_id' => subtotal]
     */
    private function calculateSellerSubtotals(array $quoteItems): array
    {
        $sellerSubtotals = [];

        foreach ($quoteItems as $item) {
            $itemSellerData = $item->getOptionByCode('option_' . SellerCartDetailsInterface::SELLER_OPTION);

            if (empty($itemSellerData)) {
                continue;
            }

            $sellerData = json_decode($itemSellerData->getValue() ?? '{}', true);
            $sellerId = $sellerData['seller_id'] ?? null;

            if (!$sellerId) {
                continue;
            }

            if (!isset($sellerSubtotals[$sellerId])) {
                $sellerSubtotals[$sellerId] = 0;
            }

            $sellerSubtotals[$sellerId] += $item->getRowTotal();
        }

        return $sellerSubtotals;
    }

    /**
     * Add free shipping rates for qualifying sellers
     *
     * @param Result $result
     * @param array $qualifyingSellers
     * @param Carrier $carrier
     * @return void
     */
    private function addPerSellerFreeShippingRates(Result $result, array $qualifyingSellers, Carrier $carrier): void
    {
        foreach ($qualifyingSellers as $sellerId => $thresholds) {
            // Get the lowest threshold for subtitle
            $lowestThreshold = min(array_map(function($threshold) {
                return $threshold->getMinOrderAmount();
            }, $thresholds));

            // Create free shipping rate for this seller
            $rate = $this->rateMethodFactory->create();
            $rate->setCarrier($carrier->getCarrierCode());
            $rate->setCarrierTitle($carrier->getConfigData('title'));
            $rate->setMethod('freeshipping_seller_' . $sellerId);
            $rate->setMethodTitle(sprintf('Free Shipping - Seller %s (Orders over €%.2f)', $sellerId, $lowestThreshold));
            $rate->setPrice(0);
            $rate->setCost(0);

            $result->append($rate);

            $this->logger->info('[CarrierPlugin] Added free shipping rate for seller', [
                'seller_id' => $sellerId,
                'threshold' => $lowestThreshold,
                'method_code' => 'freeshipping_seller_' . $sellerId
            ]);
        }
    }
}
