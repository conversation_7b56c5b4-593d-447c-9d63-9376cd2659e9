<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Plugin\Model\Quote;

use Magento\Quote\Api\Data\AddressInterface;
use Coditron\CustomShippingRate\Helper\Data as HelperData;
use Coditron\CustomShippingRate\Service\FreeShippingService;

class AddressPlugin
{
    public function __construct(
        private readonly HelperData $helper
    ) {
    }

    /**
     * @param AddressInterface $subject
     * @param callable $proceed
     * @return mixed
     */
    public function aroundCollectShippingRates(AddressInterface $subject, callable $proceed)
    {
        $found = false;
        $price = null;
        $description = null;

        //get custom shipping rate set by admin
        foreach ($subject->getAllShippingRates() as $rate) {
            if ($rate->getCode() == $subject->getShippingMethod()) {
                $found = true;
                $price = $rate->getPrice();
                $description = $rate->getMethodTitle();
                break;
            }
        }

        $return = $proceed();

        if ($found) {
            //reset custom shipping rate
            foreach ($subject->getAllShippingRates() as $rate) {
                if ($rate->getCode() == $subject->getShippingMethod()) {
                    $rate->setPrice($price);
                    $rate->setCost($price);

                    if ($rate->getCarrier() === FreeShippingService::CARRIER_CODE) {
                        $this->ensureFreeShippingThresholdSubtitle($rate, $subject);
                    } else {
                        $rate->setMethodTitle($description);
                    }
                    break;
                }
            }
        }

        return $return;
    }

    /**
     * Ensure free shipping threshold subtitle is always present
     *
     * @param \Magento\Quote\Model\Quote\Address\RateResult\Method $rate
     * @param AddressInterface $address
     * @return void
     */
    protected function ensureFreeShippingThresholdSubtitle($rate, $address)
    {
        $currentTitle = $rate->getMethodTitle();

        if (strpos($currentTitle, 'For Orders Over') !== false) {
            return;
        }

        $quote = $address->getQuote();
        if (!$quote) {
            $rate->setMethodTitle('Free Shipping');
            return;
        }

        $country = $address->getCountryId();
        $subtotal = $quote->getBaseSubtotalWithDiscount();

        $threshold = $this->helper->getFreeShippingThresholdForCountry($country, $subtotal);
        if ($threshold !== null) {
            $newTitle = sprintf('Free (For Orders Over $%.2f)', $threshold);
            $rate->setMethodTitle($newTitle);
        } else {
            $rate->setMethodTitle('Free Shipping');
        }
    }
}
