<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Service;

use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory as ShipTableRatesCollectionFactory;
use Magento\Shipping\Model\Carrier\Freeshipping;
use Psr\Log\LoggerInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

class FreeShippingService
{
    public const string CARRIER_CODE = 'freeshipping';

    public function __construct(
        private readonly ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory,
        private readonly LoggerInterface $logger,
        private readonly ScopeConfigInterface $scopeConfig
    ) {
    }

    /**
     * Get free shipping thresholds for the given country that are met
     *
     * @param string $country
     * @param float $subtotal
     * @return array
     */
    public function getMetThresholdsForCountry(string $country, float $subtotal): array
    {
        $this->logger->info('[FreeShippingService] Checking for met thresholds', [
            'country' => $country,
            'subtotal' => $subtotal
        ]);

        if (!$country || $subtotal <= 0) {
            $this->logger->info('[FreeShippingService] Invalid parameters - returning empty array', [
                'country_empty' => empty($country),
                'subtotal_invalid' => $subtotal <= 0
            ]);
            return [];
        }

        $collection = $this->shipTableRatesCollectionFactory->create();
        $collection->addFieldToFilter('free_shipping', 1)
                   ->addFieldToFilter('min_order_amount', ['gt' => 0])
                   ->addFieldToFilter('min_order_amount', ['lteq' => $subtotal])
                   ->addFieldToFilter('countries', ['like' => "%{$country}%"]);

        $this->logger->info('[FreeShippingService] Threshold query executed', [
            'sql' => $collection->getSelect()->__toString(),
            'size' => $collection->getSize()
        ]);

        $items = $collection->getItems();

        $this->logger->info('[FreeShippingService] Met thresholds found', [
            'count' => count($items),
            'thresholds' => array_map(function($item) {
                return [
                    'id' => $item->getId(),
                    'seller_id' => $item->getSellerId(),
                    'min_amount' => $item->getMinOrderAmount(),
                    'countries' => $item->getCountries(),
                    'courier' => $item->getCourier(),
                    'service_type' => $item->getServiceType()
                ];
            }, $items)
        ]);

        return $items;
    }

    /**
     * Check if there are any free shipping thresholds defined for the given country
     *
     * @param string $country
     * @return bool
     */
    public function hasThresholdsForCountry(string $country): bool
    {
        $this->logger->info('[FreeShippingService] Checking if thresholds exist for country', [
            'country' => $country
        ]);

        if (!$country) {
            $this->logger->info('[FreeShippingService] No country provided - returning false');
            return false;
        }

        $collection = $this->shipTableRatesCollectionFactory->create();
        $collection->addFieldToFilter('free_shipping', 1)
                   ->addFieldToFilter('min_order_amount', ['gt' => 0])
                   ->addFieldToFilter('countries', ['like' => "%{$country}%"])
                   ->setPageSize(1);

        $hasThresholds = $collection->getSize() > 0;

        $this->logger->info('[FreeShippingService] Threshold existence check result', [
            'has_thresholds' => $hasThresholds,
            'sql' => $collection->getSelect()->__toString()
        ]);

        return $hasThresholds;
    }

    /**
     * Override free shipping configuration to enable it
     *
     * @param Freeshipping $subject
     * @param array $metThresholds
     * @return void
     */
    public function overrideFreeShippingConfig(Freeshipping $subject, array $metThresholds): void
    {
        $this->logger->info('[FreeShippingService] Attempting to override free shipping configuration', [
            'threshold_count' => count($metThresholds)
        ]);

        if (empty($metThresholds)) {
            $this->logger->info('[FreeShippingService] No thresholds provided - skipping configuration override');
            return;
        }

        $lowestThreshold = min(array_map(function($threshold) {
            return $threshold->getMinOrderAmount();
        }, $metThresholds));

        $this->logger->info('[FreeShippingService] Calculated lowest threshold', [
            'lowest_threshold' => $lowestThreshold
        ]);

        try {
            $reflection = new \ReflectionClass($subject);
            $this->logger->info('[FreeShippingService] Reflection class created successfully', [
                'class_name' => $reflection->getName(),
                'has_config_property' => $reflection->hasProperty('_configData')
            ]);

            if ($reflection->hasProperty('_configData')) {
                $configProperty = $reflection->getProperty('_configData');
                $configProperty->setAccessible(true);
                $originalConfigData = $configProperty->getValue($subject) ?: [];

                $this->logger->info('[FreeShippingService] Original configuration data', [
                    'original_config' => $originalConfigData
                ]);

                $configData = $originalConfigData;
                $configData['free_shipping_subtotal'] = $lowestThreshold;
                $configData['active'] = 1; // Ensure it's active

                $this->logger->info('[FreeShippingService] About to set new configuration', [
                    'new_config' => $configData
                ]);

                $configProperty->setValue($subject, $configData);

                // Verify the configuration was set
                $verifyConfig = $configProperty->getValue($subject);
                $this->logger->info('[FreeShippingService] Configuration overridden successfully', [
                    'set_config' => $configData,
                    'verified_config' => $verifyConfig,
                    'overridden_subtotal' => $lowestThreshold
                ]);
            } else {
                $this->logger->warning('[FreeShippingService] Could not find _configData property in Freeshipping carrier', [
                    'available_properties' => array_map(function($prop) {
                        return $prop->getName();
                    }, $reflection->getProperties())
                ]);
            }
        } catch (\Exception $e) {
            $this->logger->error('[FreeShippingService] Error during configuration override', [
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Add threshold subtitle to free shipping method
     *
     * @param \Magento\Shipping\Model\Rate\Result $result
     * @param array $metThresholds
     * @return void
     */
    public function addThresholdSubtitle(\Magento\Shipping\Model\Rate\Result $result, array $metThresholds): void
    {
        $this->logger->info('[FreeShippingService] Adding threshold subtitles', [
            'threshold_count' => count($metThresholds),
            'rate_count' => count($result->getAllRates())
        ]);

        if (empty($metThresholds)) {
            $this->logger->info('[FreeShippingService] No thresholds provided - skipping subtitle addition');
            return;
        }

        $lowestThreshold = min(array_map(function($threshold) {
            return $threshold->getMinOrderAmount();
        }, $metThresholds));

        $this->logger->info('[FreeShippingService] Processing rates for subtitle addition', [
            'lowest_threshold' => $lowestThreshold
        ]);

        foreach ($result->getAllRates() as $rate) {
            $currentTitle = $rate->getMethodTitle();
            $this->logger->info('[FreeShippingService] Processing rate', [
                'carrier' => $rate->getCarrier(),
                'method' => $rate->getMethod(),
                'current_title' => $currentTitle,
                'is_freeshipping' => $rate->getCarrier() === self::CARRIER_CODE
            ]);

            if ($rate->getCarrier() === self::CARRIER_CODE) {
                if (strpos($currentTitle, 'For Orders Over') === false) {
                    $newTitle = sprintf('Free (For Orders Over $%.2f)', $lowestThreshold);
                    $rate->setMethodTitle($newTitle);

                    $this->logger->info('[FreeShippingService] Updated rate title', [
                        'old_title' => $currentTitle,
                        'new_title' => $newTitle
                    ]);
                } else {
                    $this->logger->info('[FreeShippingService] Rate title already contains threshold info - skipping');
                }
            }
        }
    }

    /**
     * Log the current Magento free shipping configuration
     *
     * @param int|null $storeId
     * @return void
     */
    public function logFreeShippingConfiguration(?int $storeId = null): void
    {
        $this->logger->info('[FreeShippingService] Checking Magento core free shipping configuration', [
            'store_id' => $storeId
        ]);

        $scope = $storeId ? ScopeInterface::SCOPE_STORE : ScopeConfigInterface::SCOPE_TYPE_DEFAULT;

        $config = [
            'active' => $this->scopeConfig->getValue('carriers/freeshipping/active', $scope, $storeId),
            'title' => $this->scopeConfig->getValue('carriers/freeshipping/title', $scope, $storeId),
            'name' => $this->scopeConfig->getValue('carriers/freeshipping/name', $scope, $storeId),
            'free_shipping_subtotal' => $this->scopeConfig->getValue('carriers/freeshipping/free_shipping_subtotal', $scope, $storeId),
            'specificerrmsg' => $this->scopeConfig->getValue('carriers/freeshipping/specificerrmsg', $scope, $storeId),
            'sallowspecific' => $this->scopeConfig->getValue('carriers/freeshipping/sallowspecific', $scope, $storeId),
            'specificcountry' => $this->scopeConfig->getValue('carriers/freeshipping/specificcountry', $scope, $storeId),
            'showmethod' => $this->scopeConfig->getValue('carriers/freeshipping/showmethod', $scope, $storeId),
            'sort_order' => $this->scopeConfig->getValue('carriers/freeshipping/sort_order', $scope, $storeId)
        ];

        $this->logger->info('[FreeShippingService] Magento free shipping configuration', [
            'config' => $config,
            'is_enabled' => (bool)$config['active'],
            'has_subtotal_requirement' => !empty($config['free_shipping_subtotal']) && $config['free_shipping_subtotal'] > 0
        ]);
    }

    /**
     * Log all free shipping table rates for debugging
     *
     * @return void
     */
    public function logAllFreeShippingTableRates(): void
    {
        $this->logger->info('[FreeShippingService] Fetching all free shipping table rates for debugging');

        $collection = $this->shipTableRatesCollectionFactory->create();
        $collection->addFieldToFilter('free_shipping', 1);

        $this->logger->info('[FreeShippingService] All free shipping table rates', [
            'total_count' => $collection->getSize(),
            'sql' => $collection->getSelect()->__toString(),
            'rates' => array_map(function($item) {
                return [
                    'id' => $item->getId(),
                    'seller_id' => $item->getSellerId(),
                    'courier' => $item->getCourier(),
                    'service_type' => $item->getServiceType(),
                    'countries' => $item->getCountries(),
                    'min_order_amount' => $item->getMinOrderAmount(),
                    'shipping_price' => $item->getShippingPrice(),
                    'free_shipping' => $item->getFreeShipping(),
                    'weight' => $item->getWeight()
                ];
            }, $collection->getItems())
        ]);
    }
}
