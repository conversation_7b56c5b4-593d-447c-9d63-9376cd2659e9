<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Service;

use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory as ShipTableRatesCollectionFactory;
use Magento\Shipping\Model\Carrier\Freeshipping;

class FreeShippingService
{
    public const string CARRIER_CODE = 'freeshipping';

    public function __construct(
        private readonly ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory
    ) {
    }

    /**
     * Get free shipping thresholds for the given country that are met
     *
     * @param string $country
     * @param float $subtotal
     * @return array
     */
    public function getMetThresholdsForCountry(string $country, float $subtotal): array
    {
        if (!$country || $subtotal <= 0) {
            return [];
        }

        $collection = $this->shipTableRatesCollectionFactory->create();
        $collection->addFieldToFilter('free_shipping', 1)
                   ->addFieldToFilter('min_order_amount', ['gt' => 0])
                   ->addFieldToFilter('min_order_amount', ['lteq' => $subtotal])
                   ->addFieldToFilter('countries', ['like' => "%{$country}%"]);

        return $collection->getItems();
    }

    /**
     * Override free shipping configuration to enable it
     *
     * @param Freeshipping $subject
     * @param array $metThresholds
     * @return void
     */
    public function overrideFreeShippingConfig(Freeshipping $subject, array $metThresholds): void
    {
        if (empty($metThresholds)) {
            return;
        }

        $lowestThreshold = min(array_map(function($threshold) {
            return $threshold->getMinOrderAmount();
        }, $metThresholds));

        $reflection = new \ReflectionClass($subject);
        if ($reflection->hasProperty('_configData')) {
            $configProperty = $reflection->getProperty('_configData');
            $configProperty->setAccessible(true);
            $configData = $configProperty->getValue($subject) ?: [];

            $configData['free_shipping_subtotal'] = $lowestThreshold;
            $configProperty->setValue($subject, $configData);
        }
    }

    /**
     * Add threshold subtitle to free shipping method
     *
     * @param \Magento\Shipping\Model\Rate\Result $result
     * @param array $metThresholds
     * @return void
     */
    public function addThresholdSubtitle(\Magento\Shipping\Model\Rate\Result $result, array $metThresholds): void
    {
        if (empty($metThresholds)) {
            return;
        }

        $lowestThreshold = min(array_map(function($threshold) {
            return $threshold->getMinOrderAmount();
        }, $metThresholds));

        foreach ($result->getAllRates() as $rate) {
            $currentTitle = $rate->getMethodTitle();
            if ($rate->getCarrier() === self::CARRIER_CODE) {
                if (strpos($currentTitle, 'For Orders Over') === false) {
                    $currentTitle = sprintf('Free (For Orders Over $%.2f)', $lowestThreshold);
                    $rate->setMethodTitle($currentTitle);
                }
            }
        }
    }
}
