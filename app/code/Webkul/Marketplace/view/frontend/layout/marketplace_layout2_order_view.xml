<?xml version="1.0"?>
<!--
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="seller-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="marketplace_order_view_totals"/>
    <body>
        <referenceBlock name="seller.page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">View Order Details</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="seller.content">
            <block class="Webkul\Marketplace\Block\Order\View"  name="marketplace_order_view" template="Webkul_Marketplace::order/view.phtml" cacheable="false">
                <block class="Webkul\Marketplace\Block\Order\Items" name="marketplace_order_items" template="Webkul_Marketplace::order/items.phtml" />
                <arguments>
                    <argument name="view_model" xsi:type="object">Webkul\Marketplace\ViewModel\HelperViewModel</argument>
                    <argument name="view_model_customer_email" xsi:type="object">Comave\MaskedEmail\ViewModel\CustomerEmail</argument>
                </arguments>
            </block>

        </referenceContainer>
        <referenceBlock name="marketplace_order_view">
            <arguments>
                <argument name="view_model" xsi:type="object">Webkul\Marketplace\ViewModel\HelperViewModel</argument>
                <argument name="view_model_customer_email" xsi:type="object">Comave\MaskedEmail\ViewModel\CustomerEmail</argument>
            </arguments>
            <action method="setTemplate" ifconfig="marketplace/general_settings/order_manage">
                <argument name="template" xsi:type="string">order/manageorder.phtml</argument>
            </action>
        </referenceBlock>
        <block class="Webkul\Marketplace\Block\Order\Additional\Info" name="seller.orderitem.info" template="order/additional/info.phtml">
             <arguments>
                <argument name="view_model" xsi:type="object">Webkul\Marketplace\ViewModel\HelperViewModel</argument>
            </arguments>
        </block>
    </body>
</page>
