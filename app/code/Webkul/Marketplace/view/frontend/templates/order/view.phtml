<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
$viewModel = $block->getViewModel();
$helper = $viewModel->getHelper();
$orderHelper = $viewModel->getOrderHelper();
$isPartner = $helper->isSeller();
/** @var $viewModelCustomerEmail \Comave\MaskedEmail\ViewModel\CustomerEmail */
$viewModelCustomerEmail = $block->getData('view_model_customer_email');

if ($isPartner == 1) {
    $orderId = $block->getRequest()->getParam('id');
    $order = $block->getOrder();
    $orderStatusLabel = $order->getStatusLabel();
    $paymentMethod = '';
    if ($order->getPayment()) {
        $paymentMethod = $order->getPayment()->getMethodInstance()->getTitle();
    }

    $marketplaceOrders = $block->getSellerOrderInfo($orderId);
    if (count($marketplaceOrders)) {
        $tracking=$orderHelper->getOrderinfo($orderId);
        if ($tracking->getIsCanceled()) {
            $orderStatusLabel='Canceled';
        }
        ?>
        <div class="wk_mp_design">
            <div class="fieldset wk_mp_fieldset" id="wk_mp_print_order">
                <div class="page-title-wrapper">
                    <h1 class="page-title">
                        <span data-ui-id="page-title-wrapper" class="base">
                            <?= /* @noEscape */ __('Order #%1', $order->getRealOrderId()) ?>
                        </span>
                    </h1>
                    <span class="order-status"><?= $escaper->escapeHtml($orderStatusLabel)?></span>
                    <div class="order-date">
                        <?= /* @noEscape */ __('<span class="label">Order Date:</span> %1', '<date>' . $block
                        ->formatDate($order->getCreatedAt(), \IntlDateFormatter::MEDIUM, true, $block
                        ->getTimezoneForStore($order->getStore())) . '</date>') ?>
                    </div>
                    <div class="actions-toolbar order-actions-toolbar">
                        <div class="actions">
                            <a onclick="this.target='_blank';" href="<?= $escaper->escapeUrl($block->
                            getUrl('marketplace/order/printorder', ['id'=>$orderId, '_secure' => $block
                            ->getRequest()->isSecure()]));?>" class="action print">
                                <button class="button wk_mp_btn " title="<?= /* @noEscape */ __('Print') ?>"
                                 type="submit" id="save_butn" >
                                    <span><span><?= /* @noEscape */ __('Print') ?></span></span>
                                </button>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="order-details-items ordered">
                    <div class="order-title">
                        <strong><?= /* @noEscape */ __('Items Ordered') ?></strong>
                    </div>
                    <?= $block->getChildHtml('marketplace_order_items') ?>
                </div>
                <?php if ($helper->getSellerProfileDisplayFlag()) { ?>
                    <div class="block block-order-details-view">
                        <div class="block-title">
                            <strong><?= /* @noEscape */ __('Buyer Information') ?></strong>
                        </div>
                        <div class="block-content">
                            <div class="box-content">
                                <div class="box">
                                    <div class="wk_row">
                                        <span class="label"><?= /* @noEscape */ __('Customer Name')?> : </span>
                                        <span class="value"><?= $escaper->escapeHtml($order
                                        ->getCustomerName()); ?></span>
                                    </div>
                                    <div class="wk_row">
                                        <span class="label"><?= /* @noEscape */ __('Email')?> : </span>
                                        <span class="value">
                                            <?= ($viewModelCustomerEmail)?
                                                $viewModelCustomerEmail->getCustomerEmail(
                                                    (int)$order->getCustomerId(), $order->getCustomerEmail()
                                                )
                                                : $order->getCustomerEmail()
                                            ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php } ?>
                <div class="block block-order-details-view">
                    <div class="block-title">
                        <strong><?= /* @noEscape */ __('Order Information') ?></strong>
                    </div>
                    <div class="block-content">
                        <?php if ($helper->getSellerProfileDisplayFlag()) { ?>
                            <?php if ($block->isOrderCanShip($order)): ?>
                                <div class="box box-order-shipping-address">
                                    <strong class="box-title"><span>
                                        <?= /* @noEscape */ __('Shipping Address') ?></span></strong>
                                    <div class="box-content">
                                        <address><?= /* @noEscape */ $block->getFormattedAddress($order
                                        ->getShippingAddress()); ?></address>
                                    </div>
                                </div>

                                <div class="box box-order-shipping-method">
                                    <strong class="box-title">
                                        <span><?= /* @noEscape */ __('Shipping Method') ?></span>
                                    </strong>
                                    <div class="box-content">
                                    <?php if ($order->getShippingDescription()): ?>
                                        <?= $escaper->escapeHtml($order->getShippingDescription()) ?>
                                    <?php else: ?>
                                        <?= /* @noEscape */ __('No shipping information available'); ?>
                                    <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <div class="box box-order-billing-address">
                                <strong class="box-title">
                                    <span><?= /* @noEscape */ __('Billing Address') ?></span>
                                </strong>
                                <div class="box-content">
                                    <address><?= /* @noEscape */ $block->getFormattedAddress($order
                                    ->getBillingAddress()); ?></address>
                                </div>
                            </div>
                        <?php } ?>
                        <div class="box box-order-billing-method">
                            <strong class="box-title">
                                <span><?= /* @noEscape */ __('Payment Method') ?></span>
                            </strong>
                            <div class="box-content">
                                <?= $escaper->escapeHtml($paymentMethod); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="buttons-set">
            <p class="back-link">
                <a href="<?= $escaper->escapeUrl($block
                ->getUrl('marketplace/order/history', ['_secure' => $block
                ->getRequest()->isSecure()]));?>" class="left">&laquo; <?= /* @noEscape */
                 __('Back To My Orders') ?></a>
            </p>
        </div>
        <?php
    }
} else { ?>
    <h2 class="wk_mp_error_msg">
        <?= /* @noEscape */ __("To Become Seller Please Contact to Admin."); ?>
    </h2>
    <?php
} ?>
