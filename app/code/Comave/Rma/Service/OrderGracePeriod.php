<?php

declare(strict_types=1);

namespace Comave\Rma\Service;

use Comave\Rma\Model\ConfigProvider;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Sales\Api\Data\OrderInterface;

class OrderGracePeriod
{
    /**
     * @param SellerReturnAddressProvider $addressProvider
     * @param ConfigProvider $configProvider
     * @param TimezoneInterface $dateTime
     */
    public function __construct(
        private readonly SellerReturnAddressProvider $addressProvider,
        private readonly ConfigProvider $configProvider,
        private readonly TimezoneInterface $dateTime
    ) {
    }

    /**
     * @param OrderInterface $order
     * @param int|string $orderItemId
     * @return bool
     * @throws \Exception
     */
    public function isAllowed(OrderInterface $order, int|string $orderItemId): bool
    {
        if ($order->getIsVirtual()) {
            return true;
        }

        $shippingAddress = $order->getShippingAddress();

        if ($shippingAddress === null) {
            throw new LocalizedException(
                __(
                    'Unable to determine order shipping address for order #%1',
                    $order->getIncrementId()
                )
            );
        }

        $sellerReturnAddresses = $this->addressProvider->get($order);

        if (empty($sellerReturnAddresses) || !isset($sellerReturnAddresses[$orderItemId])) {
            throw new LocalizedException(__('Unable to determine seller origin country'));
        }

        $sellerReturnAddress = $sellerReturnAddresses[$orderItemId];
        $sellerCountryId = $sellerReturnAddress['country_id'] ?? false;

        if (empty($sellerCountryId)) {
            throw new LocalizedException(
                __(
                    'Unable to determine seller origin country for address %1, seller ID %2',
                    $sellerReturnAddress['entity_id'],
                    $sellerReturnAddress['parent_id']
                )
            );
        }

        $orderCountryId = $shippingAddress->getCountryId();
        $websiteId = $order->getStore()->getWebsiteId();
        $gracePeriod = $sellerCountryId === $orderCountryId ?
            $this->configProvider->getLocalGracePeriod((int) $websiteId) :
            $this->configProvider->getInternationalGracePeriod((int) $websiteId);

        $currentDate = $this->dateTime->date()->setTime(0, 0);
        $gracePeriodDate = $this->dateTime->date(
            strtotime($order->getCreatedAt())
        )->setTime(0, 0)
        ->add(
            new \DateInterval(sprintf('P%dD', $gracePeriod))
        );

        return $currentDate->getTimestamp() <= $gracePeriodDate->getTimestamp();
    }
}
