<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="checkout_submit_all_after">
        <observer name="comave_sales_observer_checkout_submit_all_after"
                  instance="Comave\Sales\Observer\CheckoutSubmitAllAfter"/>
    </event>
    <event name="sales_order_save_after">
        <observer name="comave_sales_observer_sales_order_save_after"
                  instance="Comave\Sales\Observer\SalesOrderSaveAfter"/>
    </event>
    <event name="checkout_submit_before">
        <observer name="preventMultipleItemsInCartBeforeOrder"
                instance="Comave\Sales\Observer\PreventMultipleSellerItems"/>
    </event>
</config>
