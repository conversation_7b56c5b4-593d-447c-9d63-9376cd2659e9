<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Model\Processor\Type;

use Comave\LixApi\Model\ApiAuthorizationException;
use Comave\LixApi\Model\ConfigProvider;
use Comave\LixApi\Model\Context;
use Comave\LixApi\Model\Handler\Projects\Tasks;
use Comave\LixApiConnector\Helper\Data;
use Comave\Sales\Api\PostProcessorInterface;
use Exception;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\App\Area;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\State\InputMismatchException;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Framework\Translate\Inline\StateInterface;
use Magento\Reward\Model\RewardFactory;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class Reward implements PostProcessorInterface
{
    /**
     * @var \Magento\Customer\Api\Data\CustomerInterface|null
     */
    private ?CustomerInterface $customer = null;

    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly ConfigProvider $configProvider,
        private readonly Data $dataHelper,
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly RewardFactory $rewardFactory,
        private readonly CollectionFactory $orderCollectionFactory,
        private readonly StoreManagerInterface $storeManager,
        private readonly TransportBuilder $transportBuilder,
        private readonly StateInterface $inlineTranslation,
        private readonly Context $apiContext,
        private readonly Tasks $projectTasksHandler,
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly SerializerInterface $serializer,
    ) {
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return void
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\State\InputMismatchException
     */
    public function process(OrderInterface $order): void
    {
        if ($this->configProvider->isApiEnabled()) {

            $taskId = $this->configProvider->getTaskId();
            $everyLix = $this->dataHelper->getEveryLixTaskId();
            $firstCash = $this->dataHelper->getFirstCashTaskId();
            $everyCash = $this->dataHelper->getEveryCashTaskId();

            $this->updateBalance($order);
            $tasks = $this->getTasksList();
            foreach ($tasks as $task) {
                $firstTask = $secondTask = $cashTask = $cashSecondTask = "";
                if ($task['id'] == $taskId) {
                    $firstTask = $task['title'];
                } elseif ($task['id'] == $everyLix) {
                    $secondTask = $task['title'];
                } elseif ($task['id'] == $firstCash) {
                    $cashTask = $task['title'];
                } elseif ($task['id'] == $everyCash) {
                    $cashSecondTask = $task['title'];
                }

                $firstOrderReward = $this->dataHelper->getItemReward();
                $secondOrderReward = $this->dataHelper->getItemRewardSecond();
                $lixCountryId = $this->getCountryId($order);
                try {
                    if (!$order->getCustomerIsGuest()) {
                        $customer = $this->getCustomer((int)$order->getCustomerId());
                        $this->handleCustomerActivity($customer, $order);
                        try {
                            if ($this->isCustomerFirstOrder(['customer_id' => $order->getCustomerId()])) {
                                if ($this->isCashPointCountry($lixCountryId)) {
                                    if ($cashTask == 'Comave First Order - LIXCA') {
                                        $lixActivityData = $this->getActivityData($firstCash, $order);
                                        if (!empty($lixActivityData)) {
                                            $this->setOrderActivity($order, $lixActivityData, $firstOrderReward);
                                        }
                                    }
                                } else {
                                    if ($firstTask == 'Comave First Order - LIXX') {
                                        $lixActivityData = $this->getActivityData($taskId, $order);
                                        if (!empty($lixActivityData)) {
                                            if ($this->isLixPointCountry($lixCountryId)) {
                                                $this->setOrderActivity(
                                                    $order,
                                                    $lixActivityData,
                                                    $firstOrderReward,
                                                    false
                                                );
                                            }
                                        }
                                    }
                                }
                            } else {
                                if ($this->isCashPointCountry($lixCountryId)) {
                                    if ($cashSecondTask == 'Comave Every Order - LIXCA') {
                                        $lixActivityData = $this->getActivityData($everyCash, $order);
                                        if (!empty($lixActivityData)) {
                                            $this->setOrderActivity($order, $lixActivityData, $secondOrderReward, true);
                                        }
                                    }
                                } else {
                                    if ($secondTask == 'Comave Every Order - LIXX') {
                                        $lixActivityData = $this->getActivityData($everyLix, $order);
                                        if (!empty($lixActivityData)) {
                                            $this->setOrderActivity(
                                                $order,
                                                $lixActivityData,
                                                $secondOrderReward,
                                                false
                                            );
                                        }
                                    }
                                }
                            }
                        } catch (Exception $e) {
                            $this->logger->warning($e->getMessage());
                        }
                    } else {
                        $resEmail = $this->dataHelper->getLixUserEmail($order->getCustomerEmail());
                        if (empty($resEmail['success'])) {
                            $this->dataHelper->setLixCustomers(json_encode([
                                'name' => $order->getBillingAddress()->getFirstname(),
                                'phone' => $order->getBillingAddress()->getTelephone(),
                                'password' => sprintf("%s@123", $order->getBillingAddress()->getFirstname()),
                                'email' => $order->getCustomerEmail(),
                                'is_terms_accepted' => 1,
                                'is_privacy_policy_accepted' => 1,
                            ]));
                        }
                        $customer = $this->customerRepository->get(
                            $order->getCustomerEmail(),
                            $order->getStore()->getWebsiteId()
                        );
                        if ((int)$customer->getId()) {

                            $this->setCustomer($customer);
                            if ($this->isCustomerFirstOrder(['customer_id' => (int)$customer->getId()])) {
                                if ($this->isCashPointCountry($order->getShippingAddress()->getCountryId())) {
                                    if ($cashTask == 'Comave First Order - LIXCA') {
                                        $lixActivityData = $this->getActivityData($firstCash, $order);
                                        if (!empty($lixActivityData)) {
                                            $this->setOrderActivity($order, $lixActivityData, $firstOrderReward);
                                        }
                                    }
                                } else {
                                    if ($firstTask == 'Comave First Order - LIXX') {
                                        $lixActivityData = $this->getActivityData($taskId, $order);
                                        if (!empty($lixActivityData)) {
                                            if ($this->isLixPointCountry($lixCountryId)) {
                                                $this->setOrderActivity(
                                                    $order,
                                                    $lixActivityData,
                                                    $firstOrderReward,
                                                    false
                                                );
                                            }
                                        }
                                    }
                                }
                            } else {
                                if ($this->isCashPointCountry($lixCountryId)) {
                                    if ($cashSecondTask == 'Comave Every Order - LIXCA') {
                                        $lixActivityData = $this->getActivityData($everyCash, $order);
                                        if (!empty($lixActivityData)) {
                                            $this->setOrderActivity($order, $lixActivityData, $secondOrderReward);
                                        }
                                    }
                                } else {
                                    if ($secondTask == 'Comave Every Order - LIXX') {
                                        $lixActivityData = $this->getActivityData($everyLix, $order);
                                        if (!empty($lixActivityData)) {
                                            if ($this->isLixPointCountry($lixCountryId)) {
                                                $this->setOrderActivity(
                                                    $order,
                                                    $lixActivityData,
                                                    $secondOrderReward,
                                                    false
                                                );
                                            }
                                        }
                                    }
                                }
                            }
                        } else {
                            /** Guest User */
                            $orderCollection = $this->orderCollectionFactory->create()
                                ->addFieldToFilter('customer_id', ['null' => true])
                                ->addFieldToFilter('customer_email', $order->getCustomerEmail())
                                ->setOrder('created_at', 'ASC');
                            if ($orderCollection->count() === 1) {
                                $guestOrder = $this->orderRepository->get(
                                    (int)$orderCollection->getFirstItem()->getId()
                                );
                                if ($guestOrder->getId()) {
                                    $guestCountryId = $guestOrder->getShippingAddress()->getCountryId();
                                    if ($this->isCashPointCountry($guestCountryId)) {
                                        $lixActivityData = $this->getActivityData($everyCash, $guestOrder);
                                        if (!empty($lixActivityData['data'])) {
                                            $coinsEarned = 0;
                                            if (array_key_exists("data", $lixActivityData)) {
                                                $coinsEarned = $lixActivityData['data']['coins_earned'];
                                            }
                                            $this->setOrderActivity($guestOrder, $lixActivityData, $secondOrderReward);

                                            $templateOptions = array(
                                                'area' => Area::AREA_FRONTEND,
                                                'store' => $this->storeManager->getStore()->getId(),
                                            );
                                            $templateVars = array(
                                                'store' => $this->storeManager->getStore(),
                                                'customer_name' => $order->getBillingAddress()->getFirstname(),
                                                'message' => 'Hello World!!.',
                                                'points_balance' => $coinsEarned,
                                            );
                                            $from = array(
                                                'email' => "<EMAIL>",
                                                'name' => 'Congratulations! You have earned reward points',
                                            );
                                            $this->inlineTranslation->suspend();
                                            $transport = $this->transportBuilder->setTemplateIdentifier(
                                                'lix_reward_balance'
                                            )
                                                ->setTemplateOptions($templateOptions)
                                                ->setTemplateVars($templateVars)
                                                ->setFrom($from)
                                                ->addTo($order->getCustomerEmail())
                                                ->getTransport();
                                            $transport->sendMessage();
                                            $this->inlineTranslation->resume();

                                            $templateOptions = array(
                                                'area' => Area::AREA_FRONTEND,
                                                'store' => $this->storeManager->getStore()->getId(),
                                            );
                                            $templateVars = array(
                                                'store' => $this->storeManager->getStore(),
                                                'customer_name' => $order->getBillingAddress()->getFirstname(),
                                                'message' => 'Hello World!!.',
                                            );
                                            $from = array(
                                                'email' => "<EMAIL>",
                                                'name' => 'Invitation to Register on ComAve',
                                            );
                                            $this->inlineTranslation->suspend();
                                            $transport = $this->transportBuilder->setTemplateIdentifier(
                                                'lix_guest_register'
                                            )
                                                ->setTemplateOptions($templateOptions)
                                                ->setTemplateVars($templateVars)
                                                ->setFrom($from)
                                                ->addTo($order->getCustomerEmail())
                                                ->getTransport();
                                            $transport->sendMessage();
                                            $this->inlineTranslation->resume();
                                        }
                                    } else {
                                        $lixActivityData = $this->getActivityData($everyLix, $guestOrder);
                                        if (!empty($lixActivityData['data'])) {
                                            $coinsEarned = 0;
                                            if (array_key_exists("data", $lixActivityData)) {
                                                $coinsEarned = $lixActivityData['data']['coins_earned'];
                                            }
                                            $this->setOrderActivity(
                                                $guestOrder,
                                                $lixActivityData,
                                                $secondOrderReward,
                                                false
                                            );

                                            $templateOptions = array(
                                                'area' => Area::AREA_FRONTEND,
                                                'store' => $this->storeManager->getStore()->getId(),
                                            );
                                            $templateVars = array(
                                                'store' => $this->storeManager->getStore(),
                                                'customer_name' => $order->getBillingAddress()->getFirstname(),
                                                'message' => 'Hello World!!.',
                                                'points_balance' => $coinsEarned,
                                            );
                                            $from = array(
                                                'email' => "<EMAIL>",
                                                'name' => 'Congratulations! You have earned reward points',
                                            );
                                            $this->inlineTranslation->suspend();
                                            $transport = $this->transportBuilder
                                                ->setTemplateIdentifier('lix_reward_balance')
                                                ->setTemplateOptions($templateOptions)
                                                ->setTemplateVars($templateVars)
                                                ->setFrom($from)
                                                ->addTo($order->getCustomerEmail())
                                                ->getTransport();
                                            $transport->sendMessage();
                                            $this->inlineTranslation->resume();

                                            $templateOptions = array(
                                                'area' => Area::AREA_FRONTEND,
                                                'store' => $this->storeManager->getStore()->getId(),
                                            );
                                            $templateVars = array(
                                                'store' => $this->storeManager->getStore(),
                                                'customer_name' => $order->getBillingAddress()->getFirstname(),
                                                'message' => 'Hello World!!.',
                                            );
                                            $from = array(
                                                'email' => "<EMAIL>",
                                                'name' => 'Invitation to Register on ComAve',
                                            );
                                            $this->inlineTranslation->suspend();
                                            $transport = $this->transportBuilder->setTemplateIdentifier(
                                                'lix_guest_register'
                                            )
                                                ->setTemplateOptions($templateOptions)
                                                ->setTemplateVars($templateVars)
                                                ->setFrom($from)
                                                ->addTo($order->getCustomerEmail())
                                                ->getTransport();
                                            $transport->sendMessage();
                                            $this->inlineTranslation->resume();
                                        }
                                    }
                                }
                            } else {
                                if ($this->isCashPointCountry($order->getShippingAddress()->getCountryId())) {
                                    $lixActivityData = $this->getActivityData($everyCash, $order);
                                    if (!empty($lixActivityData['data'])) {
                                        $coinsEarned = 0;
                                        if (array_key_exists("data", $lixActivityData)) {
                                            $coinsEarned = $lixActivityData['data']['coins_earned'];
                                        }
                                        $this->setOrderActivity($order, $lixActivityData, $secondOrderReward);

                                        $templateOptions = array(
                                            'area' => Area::AREA_FRONTEND,
                                            'store' => $this->storeManager->getStore()->getId(),
                                        );
                                        $templateVars = array(
                                            'store' => $this->storeManager->getStore(),
                                            'customer_name' => $order->getBillingAddress()->getFirstname(),
                                            'message' => 'Hello World!!.',
                                            'points_balance' => $coinsEarned,
                                        );
                                        $from = array(
                                            'email' => "<EMAIL>",
                                            'name' => 'Congratulations! You have earned reward points',
                                        );
                                        $this->inlineTranslation->suspend();
                                        $transport = $this->transportBuilder->setTemplateIdentifier(
                                            'lix_reward_balance'
                                        )
                                            ->setTemplateOptions($templateOptions)
                                            ->setTemplateVars($templateVars)
                                            ->setFrom($from)
                                            ->addTo($order->getCustomerEmail())
                                            ->getTransport();
                                        $transport->sendMessage();
                                        $this->inlineTranslation->resume();

                                        $templateOptions = array(
                                            'area' => Area::AREA_FRONTEND,
                                            'store' => $this->storeManager->getStore()->getId(),
                                        );
                                        $templateVars = array(
                                            'store' => $this->storeManager->getStore(),
                                            'customer_name' => $order->getBillingAddress()->getFirstname(),
                                            'message' => 'Hello World!!.',
                                        );
                                        $from = array(
                                            'email' => "<EMAIL>",
                                            'name' => 'Invitation to Register on ComAve',
                                        );
                                        $this->inlineTranslation->suspend();
                                        $transport = $this->transportBuilder->setTemplateIdentifier(
                                            'lix_guest_register'
                                        )
                                            ->setTemplateOptions($templateOptions)
                                            ->setTemplateVars($templateVars)
                                            ->setFrom($from)
                                            ->addTo($order->getCustomerEmail())
                                            ->getTransport();
                                        $transport->sendMessage();
                                        $this->inlineTranslation->resume();
                                    }
                                } else {
                                    $lixActivityData = $this->getActivityData($everyLix, $order);
                                    if (!empty($lixActivityData['data'])) {
                                        if ($this->isLixPointCountry($order->getShippingAddress()->getCountryId())) {
                                            $coinsEarned = 0;
                                            if (array_key_exists("data", $lixActivityData)) {
                                                $coinsEarned = $lixActivityData['data']['coins_earned'];
                                            }
                                            $this->setOrderActivity(
                                                $order,
                                                $lixActivityData,
                                                $secondOrderReward,
                                                false
                                            );

                                            $templateOptions = array(
                                                'area' => Area::AREA_FRONTEND,
                                                'store' => $this->storeManager->getStore()->getId(),
                                            );
                                            $templateVars = array(
                                                'store' => $this->storeManager->getStore(),
                                                'customer_name' => $order->getBillingAddress()->getFirstname(),
                                                'message' => 'Hello World!!.',
                                                'points_balance' => $coinsEarned,
                                            );
                                            $from = array(
                                                'email' => "<EMAIL>",
                                                'name' => 'Congratulations! You have earned reward points',
                                            );
                                            $this->inlineTranslation->suspend();
                                            $transport = $this->transportBuilder->setTemplateIdentifier(
                                                'lix_reward_balance'
                                            )
                                                ->setTemplateOptions($templateOptions)
                                                ->setTemplateVars($templateVars)
                                                ->setFrom($from)
                                                ->addTo($order->getCustomerEmail())
                                                ->getTransport();
                                            $transport->sendMessage();
                                            $this->inlineTranslation->resume();

                                            $templateOptions = array(
                                                'area' => Area::AREA_FRONTEND,
                                                'store' => $this->storeManager->getStore()->getId(),
                                            );
                                            $templateVars = array(
                                                'store' => $this->storeManager->getStore(),
                                                'customer_name' => $order->getBillingAddress()->getFirstname(),
                                                'message' => 'Hello World!!.',
                                            );
                                            $from = array(
                                                'email' => "<EMAIL>",
                                                'name' => 'Invitation to Register on ComAve',
                                            );
                                            $this->inlineTranslation->suspend();
                                            $transport = $this->transportBuilder->setTemplateIdentifier(
                                                'lix_guest_register'
                                            )
                                                ->setTemplateOptions($templateOptions)
                                                ->setTemplateVars($templateVars)
                                                ->setFrom($from)
                                                ->addTo($order->getCustomerEmail())
                                                ->getTransport();
                                            $transport->sendMessage();
                                            $this->inlineTranslation->resume();
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception $exception) {
                    $this->logger->critical($exception->getMessage(), [
                        'order' => $order->getIncrementId(),
                    ]);
                }
            }
        }
    }

    /**
     * @param $customerId
     * @param $websiteId
     * @param $comment
     * @param $coinsEarned
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function setLixReward($customerId, $websiteId, $comment, $coinsEarned): void
    {
        $action = 0;
        $lixWallet = 'lix_wallet';

        $customer = $this->getCustomer($customerId);
        $lixCurrResp = $this->dataHelper->getCustLixWallet((int)$customer->getId(), $lixWallet);
        $lixBalResp = $this->dataHelper->getLixBalanceByEmail($customer->getEmail(), $lixCurrResp);

        if ($lixBalResp) {
            if (array_key_exists('data', $lixBalResp)) {
                $lixBalance = $lixBalResp['data']['balance'];
                $this->rewardFactory->create()->setCustomer($customer)
                    ->setCustomerId((int)$customer->getId())
                    ->setWebsiteId($websiteId)
                    ->setPointsBalance($lixBalance)
                    ->setAction($action)
                    ->setComment($comment)
                    ->setPointsDelta($coinsEarned)
                    ->updateRewardPoints();
            }
        }
    }

    /**
     * @param $order
     * @return bool
     */
    private function isOrderPaid($order): bool
    {
        return $order->getBaseTotalDue() == 0;
    }

    /**
     * @param int $customerId
     * @return \Magento\Customer\Api\Data\CustomerInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function getCustomer(int $customerId): CustomerInterface
    {
        if (is_null($this->customer)) {
            $this->customer = $this->customerRepository->getById($customerId);
        }

        return $this->customer;
    }

    /**
     * @param int|null $customerId
     * @return array
     */
    private function getTasksList(?int $customerId = null): array
    {
        $tasks = [];
        $projectId = (int)$this->dataHelper->getProjectId();
        $context = $this->apiContext->get($customerId);

        try {
            $tasks = $this->projectTasksHandler->setContext($context)->get($projectId);
        } catch (ApiAuthorizationException $exception) {
            $this->logger->critical($exception->getMessage(), [
                'context' => $context,
            ]);
        }

        return $tasks;
    }

    /**
     * @param int $customerId
     * @param string $attributeCode
     * @return mixed
     */
    private function getCustomerAttributeValue(int $customerId, string $attributeCode): mixed
    {
        $value = null;
        try {
            $customer = $this->getCustomer($customerId);
            if ($customer->getCustomAttribute($attributeCode)) {
                $value = $customer->getCustomAttribute($attributeCode)->getValue();
            }
        } catch (LocalizedException|NoSuchEntityException $exception) {
            $this->logger->critical($exception->getMessage(), [
                'context' => ['customerId' => $customerId, 'attributeCode' => $attributeCode],
            ]);
        }

        return $value;
    }

    /**
     * @param \Magento\Customer\Api\Data\CustomerInterface $customer
     * @return void
     */
    private function setCustomer(CustomerInterface $customer): void
    {
        $this->customer = $customer;
        try {
            $this->customerRepository->save($customer);
        } catch (LocalizedException|LocalizedException|InputMismatchException $exception) {
            $this->logger->critical($exception->getMessage(), [
                'context' => ['customerId' => (int)$customer->getId()],
            ]);
        }
    }

    /**
     * @param \Magento\Customer\Api\Data\CustomerInterface $customer
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return void
     */
    private function handleCustomerActivity(CustomerInterface $customer, OrderInterface $order): void
    {
        if (!$this->getCustomerAttributeValue((int)$customer->getId(), 'lix_uid')) {
            $emailResp = $this->dataHelper->getLixEmailExist(
                $this->dataHelper->getOrganisationId(),
                $customer->getEmail()
            );
            if (!empty($emailResp['data'])) {
                $userId = $emailResp['data']['user']['id'];
                $customer->setCustomAttribute("lix_uid", $userId);
                $this->setCustomer($customer);
            }
        }
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function getCountryId(OrderInterface $order): string
    {
        $lixCountryId = $this->getCustomerAttributeValue(
            (int)$order->getCustomerId(),
            'lix_country'
        );

        if (empty($lixCountryId)) {
            $lixCountryId = $order->getShippingAddress()->getCountryId();
            $customer = $this->getCustomer((int)$order->getCustomerId());
            $customer->setCustomAttribute(
                'lix_country',
                $order->getShippingAddress()->getCountryId()
            );
            $this->setCustomer($customer);
        }

        return (string)$lixCountryId;
    }

    /**
     * @param array $filters
     * @return bool
     */
    private function isCustomerFirstOrder(array $filters): bool
    {
        $customerOrders = $this->orderCollectionFactory->create();
        foreach ($filters as $filter => $value) {
            $customerOrders->addFieldToFilter($filter, $value);
        }

        return $customerOrders->count() === 1;
    }

    /**
     * @param string $taskId
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return array
     */
    private function getActivityData(string $taskId, OrderInterface $order): array
    {
        $itemNames = [];
        foreach ($order->getAllVisibleItems() as $item) {
            $itemNames[] = $item->getName();
        }

        return $this->dataHelper->getLixActivity(
            $taskId,
            $this->serializer->serialize([
                'item_id' => $order->getId(),
                'item_name' => implode(', ', $itemNames),
                'item_cost' => $order->getGrandTotal(),
                'phone' => $order->getBillingAddress()->getTelephone(),
                'email' => $order->getCustomerEmail(),
                'name' => $order->getCustomerName(),
                'item_currency' => $order->getOrderCurrencyCode(),
            ])
        );
    }

    /**
     * @param string $countryId
     * @return bool
     */
    private function isCashPointCountry(string $countryId): bool
    {
        return in_array($countryId, [
            "Qatar" => "QA",
            "Turkey" => "TU",
            "Saudi Arabia" => "SA",
            "Egypt" => "EG",
            "Afghanistan" => "AF",
            "Algeria" => "DZ",
            "Bangladesh" => "BG",
            "Bolivia" => "BO",
            "China" => "CH",
            "Iraq" => "IR",
            "KOSOVO" => "XK",
            "Morocco" => "MA",
            "Nepal" => "NP",
            "Republic of North Macedonia" => "MK",
            "Tunisia" => "TU",
        ]);
    }

    /**
     * @param string $countryId
     * @return bool
     */
    private function isLixPointCountry(string $countryId): bool
    {
        return in_array($countryId, [
            "Albania" => "AL",
            "American Samoa" => "AS",
            "Andorra" => "AN",
            "Angola" => "AG",
            "Anguilla" => "AI",
            "Antarctica" => "AT",
            "Antigua and Barbuda" => "AT",
            "Argentina" => "AR",
            "Armenia" => "AR",
            "Aruba" => "AB",
            "Australia" => "AU",
            "Austria" => "AU",
            "Azerbaijan" => "AZ",
            "Bahamas" => "BH",
            "Bahrain" => "BH",
            "Barbados" => "BR",
            "Belarus" => "BL",
            "Belgium" => "BE",
            "Belize" => "BL",
            "Benin" => "BE",
            "Bermuda" => "BM",
            "Bhutan" => "BT",
            "Bonaire, Sint Eustatius and Saba" => "BE",
            "Bosnia and Herzegovina" => "BI",
            "Botswana" => "BW",
            "Bouvet Island" => "BV",
            "Brazil" => "BR",
            "British Indian Ocean Territory" => "IO",
            "Brunei Darussalam" => "BR",
            "Bulgaria" => "BG",
            "Burkina Faso" => "BF",
            "Burundi" => "BD",
            "Cabo Verde" => "CP",
            "Cambodia" => "KH",
            "Cameroon" => "CM",
            "Canada" => "CA",
            "Cayman Islands" => "CY",
            "Central African Republic" => "CA",
            "Chad" => "TC",
            "Chile" => "CH",
            "Christmas Island" => "CX",
            "Cocos (Keeling) Islands" => "CC",
            "Colombia" => "CO",
            "Comoros" => "CO",
            "Congo (the Democratic Republic of the)" => "CO",
            "Congo" => "CO",
            "Cook Islands" => "CO",
            "Costa Rica" => "CR",
            "Croatia" => "HR",
            "Cuba" => "CU",
            "Curaçao" => "CU",
            "Cyprus" => "CY",
            "Czechia" => "CZ",
            "Côte d'Ivoire" => "CI",
            "Denmark" => "DN",
            "Djibouti" => "DJ",
            "Dominica" => "DM",
            "Dominican Republic" => "DO",
            "Ecuador" => "EC",
            "El Salvador" => "SL",
            "Equatorial Guinea" => "GN",
            "Eritrea" => "ER",
            "Estonia" => "ES",
            "Eswatini" => "SW",
            "Ethiopia" => "ET",
            "Falkland Islands [Malvinas]" => "FL",
            "Faroe Islands" => "FR",
            "Fiji" => "FJ",
            "Finland" => "FI",
            "France" => "FR",
            "French Guiana" => "GU",
            "French Polynesia" => "PY",
            "French Southern Territories" => "AT",
            "Gabon" => "GA",
            "Gambia" => "GM",
            "Georgia" => "GE",
            "Germany" => "DE",
            "Ghana" => "GH",
            "Gibraltar" => "GI",
            "Greece" => "GR",
            "Greenland" => "GR",
            "Grenada" => "GR",
            "Guadeloupe" => "GL",
            "Guam" => "GU",
            "Guatemala" => "GT",
            "Guernsey" => "GG",
            "Guinea" => "GI",
            "Guinea-Bissau" => "GN",
            "Guyana" => "GU",
            "Haiti" => "HT",
            "Heard Island and McDonald Islands" => "HM",
            "Holy See" => "VA",
            "Honduras" => "HN",
            "Hong Kong" => "HK",
            "Hungary" => "HU",
            "Iceland" => "IS",
            "India" => "IN",
            "Indonesia" => "ID",
            "Iran (Islamic Republic of)" => "IR",
            "Ireland" => "IR",
            "Isle of Man" => "IM",
            "Israel" => "IS",
            "Italy" => "IT",
            "Jamaica" => "JA",
            "Japan" => "JP",
            "Jersey" => "JE",
            "Jordan" => "JO",
            "Kazakhstan" => "KA",
            "Kenya" => "KE",
            "Kiribati" => "KI",
            "Korea (the Democratic People's Republic of)" => "PR",
            "Korea (the Republic of)" => "KO",
            "Kuwait" => "KW",
            "Kyrgyzstan" => "KG",
            "Lao People's Democratic Republic" => "LA",
            "Latvia" => "LV",
            "Lebanon" => "LB",
            "Lesotho" => "LS",
            "Liberia" => "LB",
            "Libya" => "LB",
            "Liechtenstein" => "LI",
            "Lithuania" => "LT",
            "Luxembourg" => "LU",
            "Macao" => "MA",
            "Madagascar" => "MD",
            "Malawi" => "MW",
            "Malaysia" => "MY",
            "Maldives" => "MD",
            "Mali" => "ML",
            "Malta" => "ML",
            "Marshall Islands" => "MH",
            "Martinique" => "MT",
            "Mauritania" => "MR",
            "Mauritius" => "MU",
            "Mayotte" => "MY",
            "Mexico" => "ME",
            "Micronesia (Federated States of)" => "FS",
            "Moldova (the Republic of)" => "MD",
            "Monaco" => "MC",
            "Mongolia" => "MN",
            "Montenegro" => "MN",
            "Montserrat" => "MS",
            "Mozambique" => "MO",
            "Myanmar" => "MMR",
            "Namibia" => "NA",
            "Nauru" => "NR",
            "Netherlands" => "NL",
            "New Caledonia" => "NC",
            "New Zealand" => "NZ",
            "Nicaragua" => "NI",
            "Niger" => "NE",
            "Nigeria" => "NG",
            "Niue" => "NI",
            "Norfolk Island" => "NF",
            "Northern Mariana Islands" => "MN",
            "Norway" => "NO",
            "Oman" => "OM",
            "Pakistan" => "PA",
            "Palau" => "PL",
            "Palestine, State of" => "PS",
            "Panama" => "PA",
            "Papua New Guinea" => "PN",
            "Paraguay" => "PR",
            "Peru" => "PE",
            "Philippines" => "PH",
            "Pitcairn" => "PC",
            "Poland" => "PO",
            "Portugal" => "PR",
            "Puerto Rico" => "PR",
            "Romania" => "RO",
            "Russian Federation" => "RU",
            "Rwanda" => "RW",
            "Réunion" => "RE",
            "Saint Barthélemy" => "BL",
            "Saint Helena, Ascension and Tristan da Cunha" => "SH",
            "Saint Kitts and Nevis" => "KN",
            "Saint Lucia" => "LC",
            "Saint Martin (French part)" => "MA",
            "Saint Pierre and Miquelon" => "SP",
            "Saint Vincent and the Grenadines" => "VC",
            "Samoa" => "WS",
            "San Marino" => "SM",
            "Sao Tome and Principe" => "ST",
            "Senegal" => "SE",
            "Serbia" => "SR",
            "Seychelles" => "SY",
            "Sierra Leone" => "SL",
            "Singapore" => "SG",
            "Sint Maarten (Dutch part)" => "SX",
            "Slovakia" => "SV",
            "Slovenia" => "SV",
            "Solomon Islands" => "SL",
            "Somalia" => "SO",
            "South Africa" => "ZA",
            "South Georgia and the South Sandwich Islands" => "SG",
            "South Sudan" => "SS",
            "Spain" => "ES",
            "Sri Lanka" => "LK",
            "Sudan" => "SD",
            "Suriname" => "SU",
            "Svalbard and Jan Mayen" => "SJ",
            "Sweden" => "SW",
            "Switzerland" => "CH",
            "Syrian Arab Republic" => "SY",
            "Taiwan (Province of China)" => "TW",
            "Tajikistan" => "TJ",
            "Tanzania, United Republic of" => "TZ",
            "Thailand" => "TH",
            "Timor-Leste" => "TL",
            "Togo" => "TG",
            "Tokelau" => "TK",
            "Tonga" => "TO",
            "Trinidad and Tobago" => "TT",
            "Turkmenistan" => "TK",
            "Turks and Caicos Islands" => "TC",
            "Tuvalu" => "TU",
            "Uganda" => "UG",
            "Ukraine" => "UK",
            "United Arab Emirates" => "AR",
            "United Kingdom of Great Britain and Northern Ireland" => "GB",
            "United States Minor Outlying Islands" => "UM",
            "United States of America" => "US",
            "Uruguay" => "UR",
            "Uzbekistan" => "UZ",
            "Vanuatu" => "VU",
            "Venezuela" => "VE",
            "Viet Nam" => "VN",
            "Virgin Islands (British)" => "VG",
            "Virgin Islands (U.S.)" => "VI",
            "Wallis and Futuna" => "WL",
            "Western Sahara" => "ES",
            "Yemen" => "YE",
            "Zambia" => "ZM",
            "Zimbabwe" => "ZW",
            "Åland Islands" => "AL",
        ]);
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @param array $lixActivityData
     * @param string $rate
     * @param bool $cash
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function setOrderActivity(
        OrderInterface $order,
        array $lixActivityData,
        string $rate,
        bool $cash = true
    ): void {
        $coinsEarned = 0;
        if (array_key_exists("data", $lixActivityData)) {
            $coinsEarned = $lixActivityData['data']['coins_earned'];
        }
        $wallet = "";
        if (array_key_exists("data", $lixActivityData)) {
            $wallet = $lixActivityData['data']['wallet_symbol'];
        }
        $lixWallet = $this->getCustomerAttributeValue((int)$order->getCustomerId(), 'lix_wallet');
        if (empty($lixWallet)) {
            $customer = $this->getCustomer((int)$order->getCustomerId());
            $customer->setCustomAttribute('lix_wallet', $wallet);
            $this->setCustomer($customer);
        }
        $lixReward = $coinsEarned * $cash ? $this->dataHelper->getCashCurrenciesValues(
        ) : $this->dataHelper->getLixxCurrenciesValues();

        try {
            $order->setLixRate($rate);
            $order->setLixCommission($lixReward);
            $order->setLixPoints($coinsEarned);
            $this->orderRepository->save($order);
        } catch (Exception $e) {
            $this->logger->warning($e->getMessage());
        }
        $comment = sprintf(
            "You've earned LIX rewards on order #%s",
            $order->getIncrementId()
        );
        if ($this->isOrderPaid($order)) {
            $this->setLixReward(
                (int)$order->getCustomerId(),
                $order->getStore()->getWebsiteId(),
                $comment,
                $coinsEarned
            );
        }
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function updateBalance(OrderInterface $order): void
    {
        if ($order->getData('reward_points_balance')) {
            $customerEmail = $this->getCustomer((int)$order->getCustomerId())->getEmail();
            $updateReward = $order->getData('reward_points_balance');
            $lixCurrResp = $this->dataHelper->getCustLixWallet($order->getCustomerId(), 'lix_wallet');
            $pointCurrency = $lixCurrResp;
            $this->dataHelper->updateLixBalance($customerEmail, $updateReward, $pointCurrency);
        }
    }
}
