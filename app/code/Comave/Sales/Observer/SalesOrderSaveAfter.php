<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Observer;

use Comave\Sales\Api\PostProcessingMessageInterface;
use Comave\Sales\Api\PostProcessingMessageInterfaceFactory;
use Comave\Sales\Model\ProcessorTypeManager;
use Comave\Sales\Model\Queue\Consumer\PlaceOrderPostProcessing;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\MessageQueue\PublisherInterface;
use Magento\Sales\Api\Data\OrderInterface;

readonly class SalesOrderSaveAfter implements ObserverInterface
{
    /**
     * @param \Comave\Sales\Api\PostProcessingMessageInterfaceFactory $messageFactory
     * @param \Magento\Framework\MessageQueue\PublisherInterface $publisher
     */
    public function __construct(
        private PostProcessingMessageInterfaceFactory $messageFactory,
        private PublisherInterface $publisher
    ) {
    }

    /**
     * @param \Magento\Framework\Event\Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        $order = $observer->getEvent()->getOrder();

        if ($this->isOrderPaid($order)) {
            /**
             * @var PostProcessingMessageInterface $rewardMessage
             */
            $rewardMessage = $this->messageFactory->create();
            $rewardMessage->setOrderId($order->getEntityId())->setProcessingType(
                ProcessorTypeManager::REWARD_TYPE_PROCESSOR
            );
            $this->publisher->publish(PlaceOrderPostProcessing::REWARD_TOPIC_NAME, $rewardMessage);
        }
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return bool
     */
    private function isOrderPaid(OrderInterface $order): bool
    {
        $baseGrandTotal = (float)$order->getBaseGrandTotal();
        $baseSubtotalCanceled = (float)$order->getBaseSubtotalCanceled();
        $baseTaxCanceled = (float)$order->getBaseTaxCanceled();
        $baseTotalPaid = (float)$order->getBaseTotalPaid();
        $totalAmountPaid = $baseGrandTotal - ($baseSubtotalCanceled + $baseTaxCanceled + $baseTotalPaid);
        $isOrderPaid = (double)$order->getBaseTotalPaid() > 0 &&
            $totalAmountPaid < 0.0001;

        if (!$order->getOrigData('base_grand_total')) {
            //New order with "Sale" payment action
            return $isOrderPaid;
        }

        return $isOrderPaid && $order->getOrigData(
                'base_grand_total'
            ) - $order->getOrigData(
                'base_subtotal_canceled'
            ) - $order->getOrigData(
                'base_tax_canceled'
            ) - $order->getOrigData(
                'base_total_paid'
            ) >= 0.0001;
    }
}