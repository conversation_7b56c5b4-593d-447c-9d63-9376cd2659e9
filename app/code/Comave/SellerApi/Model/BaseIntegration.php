<?php

declare(strict_types=1);

namespace Comave\SellerApi\Model;

use Comave\SellerApi\Api\IntegrationInterface;
use Comave\SellerApi\Api\IntegrationExtensionInterface;

class BaseIntegration implements IntegrationInterface
{
    private ?IntegrationExtensionInterface $extensionAttributes = null;
    private ?string $sellerId = null;

    /**
     * @param string|null $sellerColumnIdentifier
     * @param string|null $productColumnIdentifier
     * @param string|null $mainProductLinkTable
     * @param string|null $integrationType
     */
    public function __construct(
        private readonly ?string $sellerColumnIdentifier = '',
        private readonly ?string $productColumnIdentifier = '',
        private readonly ?string $mainProductLinkTable = '',
        private ?string $integrationType = ''
    ) {
    }

    /**
     * @inheritDoc
     */
    public function getIntegrationType(): string
    {
        return $this->integrationType;
    }

    /**
     * @inheritDoc
     */
    public function getTableLink(): string
    {
        return $this->mainProductLinkTable;
    }

    /**
     * @inheritDoc
     */
    public function getSellerColumnIdentifier(): string
    {
        return $this->sellerColumnIdentifier;
    }

    /**
     * @inheritDoc
     */
    public function getSellerColumnProduct(): string
    {
        return $this->productColumnIdentifier;
    }

    /**
     * @return \Comave\SellerApi\Api\IntegrationExtensionInterface|null
     */
    public function getExtensionAttributes(): ?IntegrationExtensionInterface
    {
        return $this->extensionAttributes;
    }

    /**
     * @param \Comave\SellerApi\Api\IntegrationExtensionInterface $extension
     * @return $this
     */
    public function setExtensionAttributes(IntegrationExtensionInterface $extension): self
    {
        $this->extensionAttributes = $extension;

        return $this;
    }

    /**
     * @param string $sellerId
     * @return self
     */
    public function setSellerId(string $sellerId): IntegrationInterface
    {
        $this->sellerId = $sellerId;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getSellerId(): ?string
    {
        return $this->sellerId;
    }

    /**
     * @param string $integrationType
     * @return IntegrationInterface
     */
    public function setIntegrationType(string $integrationType): IntegrationInterface
    {
        $this->integrationType = $integrationType;

        return $this;
    }
}
