<?php
declare(strict_types=1);

namespace Comave\MarketplaceApi\Model;

use Magento\Catalog\Model\Product\Attribute\Repository;
use Comave\MarketplaceApi\Model\TokenManager;
use Comave\MarketplaceApi\Api\ProductAttributeManagerInterface;
use Magento\Framework\Serialize\SerializerInterface;

class ProductAttributeManager implements ProductAttributeManagerInterface
{
    private $returnKeys = ['attribute_code', 'attribute_id', 'frontend_input', 'frontend_label', 'default_value', 'options'];

    /**
     * @param Repository $attributeRepository
     * @param TokenManager $tokenManager
     * @param SerializerInterface $serializer
     */
    public function __construct(
        private readonly Repository $attributeRepository,
        private readonly TokenManager $tokenManager,
        private readonly SerializerInterface $serializer
    ) {
    }

    /**
     * @param string $attributeCode
     * @return bool|string
     * @throws \Magento\Framework\Exception\AuthorizationException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getAttribute($attributeCode) :bool|string
    {
        $token = $this->tokenManager->extractTokenFromHeader();
        $sellerId = $this->tokenManager->validateAndRetrieveSellerId($token);
        if (!$sellerId) {
            $response['status'] = "Error: Invalid Seller ID";
            return $this->serializer->serialize($response);
        }
        $attributeOptions = $this->attributeRepository->get($attributeCode);

        return $this->serializer->serialize($this->stripResponse($attributeOptions));
    }

    /**
     * @param \Magento\Catalog\Api\Data\ProductAttributeInterface $data
     * @return array
     */
    private function stripResponse($data) : array
    {
        $respnse = [];
        foreach ($this->returnKeys as $key) {
            if(isset($data[$key])) {
                $respnse[$key] = $data->getData($key);
            }
            if($key == "options"){
                foreach($data->getOptions() as $option){
                    $respnse[$key][] = $option->getData();
                }
            }
        }
        return $respnse;
    }

}
