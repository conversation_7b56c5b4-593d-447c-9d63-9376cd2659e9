<?php
declare(strict_types=1);

namespace Comave\MarketplaceApi\Model;

use Comave\MarketplaceApi\Api\TokenManagerInterface;
use Magento\Framework\Exception\AuthorizationException;
use Magento\Framework\Webapi\Rest\Request;
use Magento\Integration\Model\Oauth\Token as TokenModel;

class TokenManager implements TokenManagerInterface
{
    public function __construct(
        private readonly Request $request,
        private readonly TokenModel $tokenModel
    ) {
    }

    public function extractTokenFromHeader(): string
    {
        $authHeader = $this->request->getHeader('Authorization');
        if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
            throw new AuthorizationException(__('Authorization token is missing or invalid.'));
        }
        return str_replace('Bearer ', '', $authHeader);
    }

    public function validateAndRetrieveSellerId(\Magento\Integration\Model\Oauth\Token|string $tokenDetails): int
    {
        if (is_string($tokenDetails)) {
            $tokenDetails = $this->getToken($tokenDetails);
        }

        if (!$tokenDetails->getId() || $tokenDetails->getCustomerId() === null) {
            throw new AuthorizationException(__('Invalid or unauthorized token.'));
        }

        return (int)$tokenDetails->getCustomerId();
    }

    /**
     * Get Token function
     *
     * @return \Magento\Integration\Model\Oauth\Token
     */
    public function getToken()
    {
        return $this->tokenModel->loadByToken($this->extractTokenFromHeader());
    }
}
