<?xml version="1.0"?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi/etc/webapi.xsd">
    <route url="/V1/mpapi/orders/:order_id/comments" method="POST">
        <service class="Comave\MarketplaceApi\Api\OrderCommentInterface" method="addComment"/>
        <resources>
            <resource ref="self"/>
        </resources>
    </route>

    <route method="GET" url="/V1/seller/stockItems/:productSku">
        <service class="Magento\CatalogInventory\Api\StockRegistryInterface" method="getStockItemBySku"/>
        <resources>
            <resource ref="self"/>
        </resources>
    </route>

    <route method="PUT" url="/V1/seller/products/:productSku/stockItems">
        <service class="Comave\MarketplaceApi\Api\ProductStockUpdateWithResponseInterface" method="execute"/>
        <resources>
            <resource ref="self"/>
        </resources>
    </route>

    <route method="GET" url="/V1/seller/attributes/:attributeCode">
        <service class="Comave\MarketplaceApi\Api\ProductAttributeManagerInterface" method="getAttribute"/>
        <resources>
            <resource ref="self"/>
        </resources>
    </route>

    <route method="POST" url="/V1/seller/inventory/source-items">
        <service class="Comave\MarketplaceApi\Api\SourceItemsSaveWithResponseInterface" method="execute"/>
        <resources>
            <resource ref="self"/>
        </resources>
    </route>

    <route url="/V1/mpapi/shipment" method="GET">
        <service class="Comave\MarketplaceApi\Api\ShipmentInterface" method="getShipments"/>
        <resources>
            <resource ref="self"/>
        </resources>
    </route>
</routes>
