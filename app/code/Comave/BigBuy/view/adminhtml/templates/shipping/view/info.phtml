<?php

/**
 * @var $block \Magento\Sales\Block\Adminhtml\Order\AbstractOrder
 * @var \Magento\Framework\View\Helper\SecureHtmlRenderer $secureRenderer
 */

/** @var \Magento\Shipping\Helper\Data $shippingHelper */
$shippingHelper = $block->getData('shippingHelper');
/** @var \Magento\Tax\Helper\Data $taxHelper */
$taxHelper = $block->getData('taxHelper');
?>
<?php
$order = $block->getOrder();
/** @var \Comave\BigBuy\ViewModel\Tracking $trackingViewModel */
$trackingViewModel = $block->getTrackingViewModel();
?>

<?php if ($order->getIsVirtual()):
    return '';
endif; ?>

<?php /* Shipping Method */ ?>
<div class="admin__page-section-item order-shipping-method">
    <div class="admin__page-section-item-title">
        <span class="title"><?= $block->escapeHtml(__('Shipping &amp; Handling Information')) ?></span>
    </div>
    <div class="admin__page-section-item-content">
        <?php if ($order->getTracksCollection()->count()): ?>
            <p>
                <a href="#" id="linkId" title="<?= $block->escapeHtmlAttr(__('Track Order')) ?>">
                    <?= $block->escapeHtml(__('Track Order')) ?>
                </a>
            </p>
            <?= /* @noEscape */
            $secureRenderer->renderEventListenerAsTag(
                'onclick',
                "popWin('".$block->escapeJs($shippingHelper->getTrackingPopupUrlBySalesModel($order)).
                "','trackorder','width=800,height=600,resizable=yes,scrollbars=yes')",
                'a#linkId'
            ) ?>
        <?php endif; ?>
        <?php if ($order->getShippingDescription()): ?>
            <strong><?= $block->escapeHtml($order->getShippingDescription()) ?></strong>

            <?php if ($taxHelper->displayShippingPriceIncludingTax()): ?>
                <?php $_excl = $block->displayShippingPriceInclTax($order); ?>
            <?php else: ?>
                <?php $_excl = $block->displayPriceAttribute('shipping_amount', false, ' '); ?>
            <?php endif; ?>
            <?php $_incl = $block->displayShippingPriceInclTax($order); ?>

            <?= /** @noEscape */
            $_excl ?>
            <?php if ($taxHelper->displayShippingBothPrices() && $_incl != $_excl): ?>
                (<?= $block->escapeHtml(__('Incl. Tax')) ?> <?= /** @noEscape */
                $_incl ?>)
            <?php endif; ?>
        <?php else: ?>
            <?= $block->escapeHtml(__('No shipping information available')) ?>
        <?php endif; ?>
    </div>
    <?php if ($trackingViewModel->hasTrackingNumbers($order)): ?>
        <div class="admin__page-section-bigbuy-content" style="margin-top:2rem;">
            <div class="admin__page-section-item-title">
                <span class="title"><?= $block->escapeHtml(__('BigBuy Tracking Numbers')) ?></span>
            </div>
            <div class="admin__page-section-item-content">
                <?php foreach ($trackingViewModel->getTrackingNumbers() as $tracking): ?>
                    <div style="padding:2px;">
                        <span><strong><?= $tracking['number'] ?></strong> (<?= $tracking['status'] ?> <?= $tracking['date'] ?>) </span>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
</div>
