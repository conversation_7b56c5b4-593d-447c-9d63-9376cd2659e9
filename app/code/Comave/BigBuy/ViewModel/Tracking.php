<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\ViewModel;

use Comave\BigBuy\Service\Order\TrackingService;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Sales\Api\Data\OrderInterface;

class Tracking implements ArgumentInterface
{
    /**
     * @var array|null
     */
    private ?array $trackingNumbers = null;

    /**
     * @param \Comave\BigBuy\Service\Order\TrackingService $trackingService
     */
    public function __construct(
        private readonly TrackingService $trackingService
    ) {
    }

    /**
     * @return array
     */
    public function getTrackingNumbers(): array
    {
        return $this->trackingNumbers ?: [];
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return bool
     */
    public function hasTrackingNumbers(OrderInterface $order): bool
    {
        if (is_null($this->trackingNumbers)) {
            $trackingData = $this->trackingService->fetch($order);
            $this->trackingNumbers = $this->formatTrackingData($trackingData);
        }

        return (bool)count($this->trackingNumbers);
    }

    /**
     * @param array $trackingData
     * @return array
     */
    private function formatTrackingData(array $trackingData): array
    {
        if (empty($trackingData)) {
            return [];
        }
        $trackingNumbers = [];
        foreach ($trackingData as $trackingItem) {
            foreach ($trackingItem['trackings'] as $item) {
                $tracking = [];
                $tracking['number'] = $item['trackingNumber'];
                $tracking['status'] = $item['statusDescription'];
                $tracking['date'] = date('l, Y-m-d H:m:s', strtotime($item['statusDate']));
                $trackingNumbers[] = $tracking;
            }
        }

        return $trackingNumbers;
    }
}
