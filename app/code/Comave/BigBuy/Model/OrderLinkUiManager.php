<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Model;

use Comave\BigBuy\Api\Data\OrderLinkInterface;
use Comave\BigBuy\Api\Data\OrderLinkInterfaceFactory;
use Comave\BigBuy\Api\OrderLinkListRepositoryInterface;
use Comave\BigBuy\Api\OrderLinkRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Model\AbstractModel;
use Umc\Crud\Ui\EntityUiManagerInterface;

class OrderLinkUiManager implements EntityUiManagerInterface
{
    /**
     * @param \Comave\BigBuy\Api\OrderLinkListRepositoryInterface $listRepository
     * @param \Magento\Framework\Api\SearchCriteriaBuilder $searchCriteriaBuilder
     * @param \Comave\BigBuy\Api\OrderLinkRepositoryInterface $repository
     * @param \Comave\BigBuy\Api\Data\OrderLinkInterfaceFactory $factory
     */
    public function __construct(
        private readonly OrderLinkListRepositoryInterface $listRepository,
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly OrderLinkRepositoryInterface $repository,
        private readonly OrderLinkInterfaceFactory $factory
    ) {
    }

    /**
     * @param int|null $id
     * @return \Comave\BigBuy\Api\Data\OrderLinkInterface|\Magento\Framework\Model\AbstractModel|null
     */
    public function get(?int $id)
    {
        return ($id)
            ? $this->repository->get($id)
            : $this->factory->create();
    }

    /**
     * @param \Magento\Framework\Model\AbstractModel $orderLink
     * @return void
     */
    public function save(AbstractModel $orderLink)
    {
        $this->repository->save($orderLink);
    }

    /**
     * @param int $id
     * @return void
     */
    public function delete(int $id)
    {
        $this->repository->deleteByLinkId($id);
    }

    /**
     * @param int $orderId
     * @return \Comave\BigBuy\Api\Data\OrderLinkInterface|null
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getByOrderId(int $orderId): ?OrderLinkInterface
    {
        if (!$orderId) {
            return $this->get(null);
        }

        $linkId = null;
        $orderLinks = $this->listRepository->getList(
            $this->searchCriteriaBuilder
                ->addFilter(OrderLinkInterface::ORDER_ID, $orderId)
                ->create()
        )->getItems();
        foreach ($orderLinks as $orderLink) {
            $linkId = (int)$orderLink->getLinkId();
            break;
        }

        return $this->get($linkId);
    }

    /**
     * @return array|\Comave\BigBuy\Api\Data\OrderLinkInterface[]
     */
    public function getList(): array
    {
        try {
            return $this->listRepository->getList($this->searchCriteriaBuilder->create())->getItems();
        } catch (LocalizedException $e) {
            return [];
        }
    }
}