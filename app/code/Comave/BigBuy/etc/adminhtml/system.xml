<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="seller_general">
            <label>BigBuy configuration</label>
            <tab>sellers_general</tab>
            <group id="general">
                <group id="bigbuy" sortOrder="20" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                    <label>BigBuy</label>
                    <field id="enable" type="select" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1"
                           translate="label">
                        <label>Enable/Disable</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                        <comment>Enable or disable the feature</comment>
                        <config_path>seller_settings/bigbuy_general/enable</config_path>
                    </field>
                    <field id="bigbuy_seller_email" type="text" sortOrder="20" showInWebsite="1" showInStore="1"
                           showInDefault="1" translate="label">
                        <label>Seller Email</label>
                        <depends>
                            <field id="enable">1</field>
                        </depends>
                        <validate>required-entry</validate>
                        <comment>Enter the BigBuy Seller registered email.</comment>
                        <config_path>seller_settings/bigbuy_general/seller_email</config_path>
                    </field>
                </group>
            </group>
            <group id="api">
                <group id="bigbuy" sortOrder="20" showInWebsite="1" showInStore="1" showInDefault="1"
                       translate="label">
                    <label>BigBuy</label>
                    <depends>
                        <field id="seller_settings/bigbuy_general/enable">1</field>
                    </depends>
                    <field id="general_api_connection_mode" showInWebsite="0" showInStore="0" showInDefault="1"
                           canRestore="1" sortOrder="10"
                           translate="label" type="select">
                        <label>API Mode</label>
                        <source_model>Comave\BigBuy\Model\Config\Source\Connection\Mode</source_model>
                        <config_path>seller_settings/bigbuy_api/general_api_connection_mode</config_path>
                    </field>
                    <field id="general_api_production_endpoint" type="text" sortOrder="20" showInWebsite="0"
                           showInStore="0" showInDefault="1" canRestore="1" translate="label">
                        <label>API Endpoint</label>
                        <validate>required-entry</validate>
                        <config_path>seller_settings/bigbuy_api/general_api_production_endpoint</config_path>
                        <depends>
                            <field id="general_api_connection_mode">1</field>
                        </depends>
                    </field>
                    <field id="general_api_production_key" type="obscure" sortOrder="30" showInWebsite="0"
                           showInStore="0"
                           showInDefault="1" translate="label">
                        <label>API Key</label>
                        <validate>required-entry</validate>
                        <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                        <config_path>seller_settings/bigbuy_api/general_api_production_key</config_path>
                        <depends>
                            <field id="general_api_connection_mode">1</field>
                        </depends>
                    </field>
                    <field id="general_api_sandbox_endpoint" type="text" sortOrder="40" showInWebsite="0"
                           showInStore="0" showInDefault="1" canRestore="1" translate="label">
                        <label>API Endpoint</label>
                        <validate>required-entry</validate>
                        <config_path>seller_settings/bigbuy_api/general_api_sandbox_endpoint</config_path>
                        <depends>
                            <field id="general_api_connection_mode">0</field>
                        </depends>
                    </field>
                    <field id="general_api_sandbox_key" type="obscure" sortOrder="50" showInWebsite="0" showInStore="0"
                           showInDefault="1" translate="label">
                        <label>API Key</label>
                        <validate>required-entry</validate>
                        <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                        <config_path>seller_settings/bigbuy_api/general_api_sandbox_key</config_path>
                        <depends>
                            <field id="general_api_connection_mode">0</field>
                        </depends>
                    </field>
                    <field id="order_sync" type="select" sortOrder="60" showInStore="0" showInDefault="1"
                           canRestore="1" translate="label">
                        <label>Enable Order Sync (push)</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                        <comment>Enable or disable order sync</comment>
                        <config_path>seller_settings/bigbuy_api/order_sync</config_path>
                    </field>
                    <field id="category_sync" type="select" sortOrder="70" showInStore="0" showInDefault="1"
                           canRestore="1" translate="label">
                        <label>Enable Category Sync (pull)</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                        <comment>Enable or disable category sync</comment>
                        <config_path>seller_settings/bigbuy_api/category_sync</config_path>
                    </field>
                    <field id="brand_sync" type="select" sortOrder="80" showInStore="0" showInDefault="1"
                           canRestore="1" translate="label">
                        <label>Enable Brand Sync (pull)</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                        <comment>Enable or disable brand sync</comment>
                        <config_path>seller_settings/bigbuy_api/brand_sync</config_path>
                    </field>
                    <field id="tracking_sync" type="select" sortOrder="90" showInStore="0" showInDefault="1"
                           canRestore="1" translate="label">
                        <label>Enable Order Tracking Sync (pull)</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                        <comment>Enable or disable order tracking sync</comment>
                        <config_path>seller_settings/bigbuy_api/tracking_sync</config_path>
                    </field>
                    <field id="order_status_sync" type="select" sortOrder="100" showInStore="0" showInDefault="1"
                           canRestore="1" translate="label">
                        <label>Enable Order Status Sync (pull)</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                        <comment>Enable or disable order status sync</comment>
                        <config_path>seller_settings/bigbuy_api/order_status_sync</config_path>
                    </field>
                </group>
            </group>
        </section>
    </system>
</config>
