<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Service\Order;

use Comave\BigBuy\Model\ConfigProvider;
use Comave\BigBuy\Model\OrderLinkUiManager;
use Comave\SellerApi\Api\ConfigurableApiInterfaceFactory;
use Comave\SellerApi\Service\RequestHandler;
use Exception;
use Laminas\Http\Request;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Psr\Http\Client\ClientExceptionInterface;
use Psr\Log\LoggerInterface;

class TrackingService
{
    /**
     * @param \Magento\Framework\Serialize\SerializerInterface $serializer
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Comave\SellerApi\Service\RequestHandler $requestHandler
     * @param \Comave\BigBuy\Model\ConfigProvider $configProvider
     * @param \Comave\SellerApi\Api\ConfigurableApiInterfaceFactory $configurableApiFactory
     * @param \Comave\BigBuy\Model\OrderLinkUiManager $orderLinkUiManager
     */
    public function __construct(
        private readonly SerializerInterface $serializer,
        private readonly LoggerInterface $logger,
        private readonly RequestHandler $requestHandler,
        private readonly ConfigProvider $configProvider,
        private readonly ConfigurableApiInterfaceFactory $configurableApiFactory,
        private readonly OrderLinkUiManager $orderLinkUiManager,
    ) {
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return array
     */
    public function fetch(OrderInterface $order): array
    {
        if (!$this->configProvider->isTrackingSyncEnable()) {
            $this->logger->warning('BigBuy Tracking Sync process is disabled');

            return [];
        }

        if (empty($this->configProvider->getApiEndpoint())) {
            $this->logger->warning('BigBuy API endpoint is not configured');

            return [];
        }

        $result = [];
        try {
            $orderLink = $this->orderLinkUiManager->getByOrderId((int)$order->getId());
            if (!empty($orderLink->getBigBuyOrderId())) {
                $configurableApi = $this->configurableApiFactory->create([
                    'method' => Request::METHOD_GET,
                    'endpoint' => $this->configProvider->getTrackingSyncEndpoint($orderLink->getBigBuyOrderId()),
                    'headers' => [
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json',
                        'Authorization' => sprintf('Bearer %s', $this->configProvider->getApiKey()),
                    ],
                    'params' => $this->serializer->serialize(['isoCode' => 'en']),
                ]);
                $response = $this->requestHandler->handleRequest($configurableApi);
                if (!$response->hasError()) {
                    $result = $this->serializer->unserialize(
                        $response->getResult()->getBody()->getContents()
                    );
                }
            }
        } catch (Exception|ClientExceptionInterface $exception) {
            $this->logger->critical("No tracking information found for this order", [
                'reason' => $exception->getMessage(),
                'order_id' => $order->getId(),
            ]);
        }

        return $result;
    }
}
