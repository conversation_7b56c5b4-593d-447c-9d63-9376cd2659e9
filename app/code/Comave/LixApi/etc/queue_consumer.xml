<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/consumer.xsd">
    <consumer name="lix.post-register.process"
              queue="lix.post-register.process"
              connection="amqp"
              onlySpawnWhenMessageAvailable="1"
              handler="Comave\LixApi\Model\Queue\Consumer\RegisterHandler::execute"/>
</config>
