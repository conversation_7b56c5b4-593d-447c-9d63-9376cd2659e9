<?php

declare(strict_types=1);

namespace Comave\LixApi\Model\Command;

use Comave\LixApi\Api\Data\RegisterRequestMessageInterface;
use Comave\LixApiConnector\Helper\Data;
use Comave\LixApiConnector\Model\Service\UserLixToken;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\Translate\Inline\StateInterface;
use Magento\Store\Api\StoreRepositoryInterface;
use Psr\Log\LoggerInterface;

class LixNewCustomerHandler
{
    /**
     * @param ScopeConfigInterface $scopeConfig
     * @param LoggerInterface $logger
     * @param Data $dataHelper
     * @param StoreRepositoryInterface $storeRepository
     * @param TransportBuilder $transportBuilder
     * @param StateInterface $inlineTranslation
     */
    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly LoggerInterface $logger,
        private readonly Data $dataHelper,
        private readonly StoreRepositoryInterface $storeRepository,
        private readonly TransportBuilder $transportBuilder,
        private readonly StateInterface $inlineTranslation
    ) {
    }

    /**
     * @param CustomerInterface $customer
     * @param RegisterRequestMessageInterface $registerRequestMessage
     * @return bool
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Store\Model\StoreIsInactiveException
     */
    public function handle(CustomerInterface $customer, RegisterRequestMessageInterface $registerRequestMessage): bool
    {
        $lixUserEmail = $this->dataHelper->getLixUserEmail($customer->getEmail());
        if (!empty($lixUserEmail['success'])) {
            return false;
        }

        $customerName = sprintf(
            '%s %s',
            $customer->getFirstname(),
            $customer->getLastname()
        );
        $newPass = $this->getNewPass();
        $customerData = [
            'name' => $customerName,
            'phone' => $registerRequestMessage->getPhoneNo() ?: (string) mt_rand(1000000, 99999999),
            'password' => $newPass,
            'email' => $customer->getEmail(),
            'is_terms_accepted' => 1,
            'is_privacy_policy_accepted' => 1,
            'club_id' => $customer->getCustomAttribute('sports_club')?->getValue()
        ];

        $postCustData = json_encode($customerData);
        $response = $this->dataHelper->setLixCustomers($postCustData);

        if (!empty($response['data'])) {
            $customer->setCustomAttribute('lix_uid',  $response['data']['id']);
        }

        $tokenResponse = $this->dataHelper->setUserToken(
            $customer->getEmail(),
            $newPass,
            'ComAve Integration',
            'n/a'
        ) ?: [];

        if (is_array($tokenResponse) && !empty($tokenResponse['data']['user-token'])) {
            $customer->setCustomAttribute(
                UserLixToken::LIX_TOKEN_ATTR_CODE,
                $tokenResponse['data']['user-token']
            );
        }

        $templateOptions = [
            'area' => \Magento\Framework\App\Area::AREA_FRONTEND,
            'store' => $customer->getStoreId()
        ];
        $templateVars = [
            'store' => $this->storeRepository->getActiveStoreById($customer->getStoreId()),
            'customer_name' => $customerName,
            'email' => $customer->getEmail(),
            'password' => $newPass,
            'message' => 'LIX WALLET'
        ];

        $from = [
            'email' => $this->scopeConfig->getValue(
                'trans_email/ident_sales/email',
                \Magento\Store\Model\ScopeInterface::SCOPE_STORE
            ),
            'name' => __('You are registered to Libra Incentix. Please reset password.')->render()
        ];

        $this->inlineTranslation->suspend();

        try {
            $transport = $this->transportBuilder->setTemplateIdentifier('lix_reset_template')
                ->setTemplateOptions($templateOptions)
                ->setTemplateVars($templateVars)
                ->setFromByScope($from)
                ->addTo($customer->getEmail())
                ->getTransport();
            $transport->sendMessage();
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }

        $this->inlineTranslation->resume();
        return true;
    }

    /**
     * @return string
     */
    private function getNewPass(): string
    {
        $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        $password = substr(str_shuffle($chars), 0, 12);
        $password[rand(0,12)] = rand(0,9);  // Ensure it contain at least one number, required by Lix
        return $password;
    }
}
