<?php

declare(strict_types=1);

namespace Comave\LixApi\Model\Command;

use Comave\ComaveApi\Api\ReferFriendManagementInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Psr\Log\LoggerInterface;

class AcceptReferral
{
    /**
     * @param ReferFriendManagementInterface $referFriendManagement
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ReferFriendManagementInterface $referFriendManagement,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @param CustomerInterface $customer
     * @return void
     */
    public function execute(CustomerInterface $customer): void
    {
        try {
            $this->referFriendManagement->acceptInvitation(
                $customer->getWebsiteId(),
                $customer->getId(),
                $customer->getEmail(),
                $customer->getCustomAttribute('referral_code')->getValue()
            );
        } catch (\Exception $e) {
            $this->logger->error(
                '[RegisterSuccessLixReferAccept] Error attempting to accept referral',
                [
                    'message' => $e->getMessage(),
                ]
            );
        }
    }
}
