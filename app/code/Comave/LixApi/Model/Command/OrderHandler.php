<?php

declare(strict_types=1);

namespace Comave\LixApi\Model\Command;

use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory;

class OrderHandler
{
    /**
     * @param CollectionFactory $collectionFactory
     */
    public function __construct(private readonly CollectionFactory $collectionFactory)
    {
    }

    /**
     * @param CustomerInterface $customer
     * @return bool
     */
    public function handle(CustomerInterface $customer): bool
    {
        $orderCollection = $this->collectionFactory->create();
        /** @var Order $order */
        $order = $orderCollection
            ->addFieldToFilter('customer_email', $customer->getEmail())
            ->addFieldToFilter('customer_is_guest', 1)
            ->setOrder('created_at', 'ASC')
            ->setPageSize(1)
            ->getFirstItem();

        if (!$order->getEntityId()) {
            return false;
        }

        $shippingAddress = $order->getShippingAddress();
        $cstCountryId = $shippingAddress->getCountryId();

        if ($customer->getCustomAttribute('lix_country')) {
            $lixCountry = $customer->getCustomAttribute('lix_country')?->getValue();
        }

        if (empty($lixCountry)) {
            $customer->setCustomAttribute('lix_country', $cstCountryId);
        }

        if ($customer->getCustomAttribute('lix_wallet')) {
            $lixWallet = $customer->getCustomAttribute('lix_wallet')->getValue();
        }

        return true;
    }
}
