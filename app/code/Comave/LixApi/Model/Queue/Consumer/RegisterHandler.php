<?php

declare(strict_types=1);

namespace Comave\LixApi\Model\Queue\Consumer;

use Comave\LixApi\Api\Data\RegisterRequestMessageInterface;
use Comave\LixApi\Model\Command\AcceptReferral;
use Comave\LixApi\Model\Command\CustomerClubHandler;
use Comave\LixApi\Model\Command\LixNewCustomerHandler;
use Comave\LixApi\Model\Command\LixTasksHandler;
use Comave\LixApi\Model\Command\OrderHandler;
use Comave\LixApi\Model\Command\TravelerInfoHandler;
use Comave\LixApi\Model\ConfigProvider;
use Comave\LixApiConnector\Helper\Data;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Directory\Model\CountryFactory;
use Psr\Log\LoggerInterface;

class RegisterHandler
{
    /**
     * @param TravelerInfoHandler $travelerInfoHandler
     * @param AcceptReferral $acceptReferralService
     * @param Data $dataHelper
     * @param CountryFactory $countryFactory
     * @param LoggerInterface $logger
     * @param CustomerClubHandler $customerClubHandler
     * @param LixNewCustomerHandler $lixNewCustomerHandler
     * @param LixTasksHandler $lixTasksHandler
     * @param OrderHandler $orderHandler
     * @param CustomerRepositoryInterface $customerRepository
     * @param ConfigProvider $configProvider
     */
    public function __construct(
        private readonly TravelerInfoHandler $travelerInfoHandler,
        private readonly AcceptReferral $acceptReferralService,
        private readonly Data $dataHelper,
        private readonly CountryFactory $countryFactory,
        private readonly LoggerInterface $logger,
        private readonly CustomerClubHandler $customerClubHandler,
        private readonly LixNewCustomerHandler $lixNewCustomerHandler,
        private readonly LixTasksHandler $lixTasksHandler,
        private readonly OrderHandler $orderHandler,
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly ConfigProvider $configProvider,
    ) {
    }

    /**
     * @param RegisterRequestMessageInterface $registerRequestMessage
     * @return void
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\State\InputMismatchException
     */
    public function execute(RegisterRequestMessageInterface $registerRequestMessage): void
    {
        try {
            $customer = $this->customerRepository->get($registerRequestMessage->getCustomerEmail());
            $this->logger->error(
                '[LixApiRegisterHandler] Starting process for customer',
                [
                    'email' => $customer->getEmail()
                ]
            );
        } catch (\Exception $e) {
            $this->logger->error(
                '[LixApiRegisterHandler] Unable to locate customer',
                [
                    'message' => $e->getMessage(),
                ]
            );

            return;
        }

        $this->logger->info(
            '[LixApiRegisterHandler] Starting processing traveller info',
        );
        $this->travelerInfoHandler->handle($customer);
        $this->logger->info(
            '[LixApiRegisterHandler] Finished processing traveller info',
        );

        $canSaveCustomer = false;
        $defaultPhoneNumber = sprintf(
            '%s%s',
            $registerRequestMessage->getDialCode() ?? '',
            $registerRequestMessage->getPhoneNo() ?? ''
        );

        if (!empty($defaultPhoneNumber)) {
            $canSaveCustomer = true;
            $customer->setCustomAttribute(
                'default_phone_number',
                $defaultPhoneNumber
            );
            $customer->setCustomAttribute(
                'phone_no',
                $defaultPhoneNumber
            );
        }
        $customer->setData('ignore_validation_flag', true);

        $referralCode = $customer->getCustomAttribute('referral_code')?->getValue();
        if (!empty($referralCode)) {
            $this->acceptReferralService->execute($customer);
            $this->logger->info(
                '[LixApiRegisterHandler] Finished processing referral acceptance',
            );
        }

        if ($registerRequestMessage->getCustomerClub()) {
            $this->customerClubHandler->execute($registerRequestMessage->getCustomerClub());
            $this->logger->info(
                '[LixApiRegisterHandler] Finished processing customer club',
            );
        }

        $isEnabled = $this->configProvider->isApiEnabled();

        if (!$isEnabled){
            if (!empty($registerRequestMessage->getCustomerCountry())) {
                $customer->setCustomAttribute('customer_country', $registerRequestMessage->getCustomerCountry());
                $this->customerRepository->save($customer);
            }

            return;
        }

        if ($registerRequestMessage->getCustomerCountry()) {
            $country = $this->countryFactory->create()->load(
                $registerRequestMessage->getCustomerCountry(),
                'iso2_code'
            );
            if ($country && $country->getCountryId()) {
                $canSaveCustomer = true;
                $countryId = $country->getCountryId();
                $customer->setCustomAttribute('customer_country', $registerRequestMessage->getCustomerCountry());
            }
        }

        try {
            $canSaveCustomer = $this->orderHandler->handle($customer) || $canSaveCustomer;
            $canSaveCustomer = $this->lixNewCustomerHandler->handle($customer, $registerRequestMessage) || $canSaveCustomer;
            $canSaveCustomer = $this->lixTasksHandler->handle($customer) || $canSaveCustomer;
        } catch (\Exception $e) {
            $this->logger->error(
                '[LixApiRegisterHandler] General error occurred',
                [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]
            );
        }

        if ($canSaveCustomer) {
            try {
                $this->customerRepository->save($customer);
            } catch (\Exception $e) {
                $this->logger->error(
                    '[LixApiRegisterHandler] Unable to save customer',
                    [
                        'message' => $e->getMessage(),
                    ]
                );
            }
        }
    }

    /**
     * Set lix_wallet attribute based on the country code
     * @todo think of a better way to achieve this....
     * @param string $countryCode
     * @return string
     */
    private function setLixWalletAttribute(string $countryCode): string
    {
        $cashPointCountry = ["Qatar" => "QA", "Turkey" => "TR", "Saudi Arabia" => "SA", "Egypt" => "EG", "Afghanistan" => "AF", "Algeria" => "DZ", "Bangladesh" => "BD", "Bolivia (Plurinational State of)" => "BO", "China" => "CN", "Iraq" => "IQ", "KOSOVO" => "XK", "Morocco" => "MA", "Nepal" => "NP", "Republic of North Macedonia" => "MK", "Tunisia" => "TN"];
        $lixPointCountry = ["Albania" => "AL", "American Samoa" => "AS", "Andorra" => "AD", "Angola" => "AO", "Anguilla" => "AI", "Antarctica" => "AQ", "Antigua and Barbuda" => "AG", "Argentina" => "AR", "Armenia" => "AM", "Aruba" => "AW", "Australia" => "AU", "Austria" => "AT", "Azerbaijan" => "AZ", "Bahamas" => "BS", "Bahrain" => "BH", "Barbados" => "BB", "Belarus" => "BY", "Belgium" => "BE", "Belize" => "BZ", "Benin" => "BJ", "Bermuda" => "BM", "Bhutan" => "BT", "Bonaire, Sint Eustatius and Saba" => "BQ", "Bosnia and Herzegovina" => "BA", "Botswana" => "BW", "Bouvet Island" => "BV", "Brazil" => "BR", "British Indian Ocean Territory" => "IO", "Brunei Darussalam" => "BN", "Bulgaria" => "BG", "Burkina Faso" => "BF", "Burundi" => "BI", "Cabo Verde" => "CV", "Cambodia" => "KH", "Cameroon" => "CM", "Canada" => "CA", "Cayman Islands" => "KY", "Central African Republic" => "CF", "Chad" => "TD", "Chile" => "CL", "Christmas Island" => "CX", "Cocos (Keeling) Islands" => "CC", "Colombia" => "CO", "Comoros" => "KM", "Congo (the Democratic Republic of the)" => "CD", "Congo" => "CG", "Cook Islands" => "CK", "Costa Rica" => "CR", "Croatia" => "HR", "Cuba" => "CU", "Curaçao" => "CW", "Cyprus" => "CY", "Czechia" => "CZ", "Côte d'Ivoire" => "CI", "Denmark" => "DK", "Djibouti" => "DJ", "Dominica" => "DM", "Dominican Republic" => "DO", "Ecuador" => "EC", "El Salvador" => "SV", "Equatorial Guinea" => "GQ", "Eritrea" => "ER", "Estonia" => "EE", "Eswatini" => "SZ", "Ethiopia" => "ET", "Falkland Islands [Malvinas]" => "FK", "Faroe Islands" => "FO", "Fiji" => "FJ", "Finland" => "FI", "France" => "FR", "French Guiana" => "GF", "French Polynesia" => "PF", "French Southern Territories" => "TF", "Gabon" => "GA", "Gambia" => "GM", "Georgia" => "GE", "Germany" => "DE", "Ghana" => "GH", "Gibraltar" => "GI", "Greece" => "GR", "Greenland" => "GL", "Grenada" => "GD", "Guadeloupe" => "GP", "Guam" => "GU", "Guatemala" => "GT", "Guernsey" => "GG", "Guinea" => "GN", "Guinea-Bissau" => "GW", "Guyana" => "GY", "Haiti" => "HT", "Heard Island and McDonald Islands" => "HM", "Holy See" => "VA", "Honduras" => "HN", "Hong Kong" => "HK", "Hungary" => "HU", "Iceland" => "IS", "India" => "IN", "Indonesia" => "ID", "Iran (Islamic Republic of)" => "IR", "Ireland" => "IE", "Isle of Man" => "IM", "Israel" => "IL", "Italy" => "IT", "Jamaica" => "JA", "Japan" => "JP", "Jersey" => "JE", "Jordan" => "JO", "Kazakhstan" => "KZ", "Kenya" => "KE", "Kiribati" => "KI", "Korea (the Democratic People's Republic of)" => "KP", "Korea (the Republic of)" => "KR", "Kuwait" => "KW", "Kyrgyzstan" => "KG", "Lao People's Democratic Republic" => "LA", "Latvia" => "LV", "Lebanon" => "LB", "Lesotho" => "LS", "Liberia" => "LR", "Libya" => "LY", "Liechtenstein" => "LI", "Lithuania" => "LT", "Luxembourg" => "LU", "Macao" => "MO", "Madagascar" => "MG", "Malawi" => "MW", "Malaysia" => "MY", "Maldives" => "MV", "Mali" => "ML", "Malta" => "MT", "Marshall Islands" => "MH", "Martinique" => "MQ", "Mauritania" => "MR", "Mauritius" => "MU", "Mayotte" => "YT", "Mexico" => "MX", "Micronesia (Federated States of)" => "FM", "Moldova (the Republic of)" => "MD", "Monaco" => "MC", "Mongolia" => "MN", "Montenegro" => "ME", "Montserrat" => "MS", "Mozambique" => "MZ", "Myanmar" => "MM", "Namibia" => "NA", "Nauru" => "NR", "Netherlands" => "NL", "New Caledonia" => "NC", "New Zealand" => "NZ", "Nicaragua" => "NI", "Niger" => "NE", "Nigeria" => "NG", "Niue" => "NU", "Norfolk Island" => "NF", "Northern Mariana Islands" => "MP", "Norway" => "NO", "Oman" => "OM", "Pakistan" => "PK", "Palau" => "PW", "Palestine, State of" => "PS", "Panama" => "PA", "Papua New Guinea" => "PG", "Paraguay" => "PY", "Peru" => "PE", "Philippines" => "PH", "Pitcairn" => "PN", "Poland" => "PL", "Portugal" => "PT", "Puerto Rico" => "PR", "Romania" => "RO", "Russian Federation" => "RU", "Rwanda" => "RW", "Réunion" => "RE", "Saint Barthélemy" => "BL", "Saint Helena, Ascension and Tristan da Cunha" => "SH", "Saint Kitts and Nevis" => "KN", "Saint Lucia" => "LC", "Saint Martin (French part)" => "MF", "Saint Pierre and Miquelon" => "PM", "Saint Vincent and the Grenadines" => "VC", "Samoa" => "WS", "San Marino" => "SM", "Sao Tome and Principe" => "ST", "Senegal" => "SN", "Serbia" => "RS", "Seychelles" => "SC", "Sierra Leone" => "SL", "Singapore" => "SG", "Sint Maarten (Dutch part)" => "SX", "Slovakia" => "SK", "Slovenia" => "SI", "Solomon Islands" => "SB", "Somalia" => "SO", "South Africa" => "ZA", "South Georgia and the South Sandwich Islands" => "GS", "South Sudan" => "SS", "Spain" => "ES", "Sri Lanka" => "LK", "Sudan" => "SD", "Suriname" => "SR", "Svalbard and Jan Mayen" => "SJ", "Sweden" => "SE", "Switzerland" => "CH", "Syrian Arab Republic" => "SY", "Taiwan (Province of China)" => "TW", "Tajikistan" => "TJ", "Tanzania, United Republic of" => "TZ", "Thailand" => "TH", "Timor-Leste" => "TL", "Togo" => "TG", "Tokelau" => "TK", "Tonga" => "TO", "Trinidad and Tobago" => "TT", "Turkmenistan" => "TM", "Turks and Caicos Islands" => "TC", "Tuvalu" => "TV", "Uganda" => "UG", "Ukraine" => "UA", "United Arab Emirates" => "AE", "United Kingdom of Great Britain and Northern Ireland" => "GB", "United States Minor Outlying Islands" => "UM", "United States of America" => "US", "Uruguay" => "UY", "Uzbekistan" => "UZ", "Vanuatu" => "VU", "Venezuela (Bolivarian Republic of)" => "VE", "Viet Nam" => "VN", "Virgin Islands (British)" => "VG", "Virgin Islands (U.S.)" => "VI", "Wallis and Futuna" => "WF", "Western Sahara" => "EH", "Yemen" => "YE", "Zambia" => "ZM", "Zimbabwe" => "ZW", "Åland Islands" => "AX", "Netherlands Antilles" => "AN"];

        $newValue = '';
        // Determine the value based on the country code
        if (in_array($countryCode, $cashPointCountry)) {
            return (string) $this->dataHelper->getLixcaWallet();
        } elseif (in_array($countryCode, $lixPointCountry)) {
            return (string) $this->dataHelper->getLixxWallet();
        }

        return $newValue;
    }
}
