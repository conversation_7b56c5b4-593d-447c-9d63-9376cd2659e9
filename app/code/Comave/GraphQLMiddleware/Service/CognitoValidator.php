<?php

declare(strict_types=1);

namespace Comave\GraphQLMiddleware\Service;

use Comave\GraphQLMiddleware\Exception\CognitoEncryptionKeyException;
use Comave\GraphQLMiddleware\Exception\InvalidCognitoTokenException;
use Comave\GraphQLMiddleware\Model\Command\ValidateToken;
use Comave\GraphQLMiddleware\Model\ConfigProvider;
use Comave\GraphQLMiddleware\Setup\Patch\Data\InstallCustomerCognitoIdAttribute;
use Magento\Authorization\Model\UserContextInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\Session;
use Magento\CustomerGraphQl\Model\Customer\CreateCustomerAccount;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Jwt\Payload\ClaimsPayload;
use Magento\Integration\Api\Data\UserToken;
use Magento\Integration\Api\Exception\UserTokenException;
use Magento\Integration\Api\UserTokenReaderInterface;
use Magento\JwtUserToken\Model\Data\Header;
use Magento\JwtUserToken\Model\Data\JwtTokenData;
use Magento\JwtUserToken\Model\Data\JwtUserContext;
use Magento\Store\Api\StoreRepositoryInterface;
use Psr\Log\LoggerInterface;

class CognitoValidator implements UserTokenReaderInterface
{
    /**
     * @param RequestInterface $request
     * @param LoggerInterface $logger
     * @param CustomerRepositoryInterface $customerRepository
     * @param CreateCustomerAccount $createCustomerAccount
     * @param ValidateToken $validateToken
     * @param Session $customerSession
     * @param StoreRepositoryInterface $storeRepository
     * @param ConfigProvider $configProvider
     */
    public function __construct(
        private readonly RequestInterface $request,
        private readonly LoggerInterface $logger,
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly CreateCustomerAccount $createCustomerAccount,
        private readonly ValidateToken $validateToken,
        private readonly Session $customerSession,
        private readonly StoreRepositoryInterface $storeRepository,
        private readonly ConfigProvider $configProvider
    ) {
    }

    /**
     * @param string $token
     * @return UserToken
     * @throws NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\GraphQl\Exception\GraphQlInputException
     * @throws \Magento\Store\Model\StoreIsInactiveException
     */
    public function read(string $token): UserToken
    {
        $this->customerSession->logout();

        if (!$this->configProvider->isCognitoActive()) {
            $this->logger->info(
                '[CognitoAuthorization] Cognito token analysis disabled',
            );

            throw new UserTokenException('Cognito Disabled');
        }

        $currentStoreCode = $this->request->getHeader('Store', 'english');

        try {
            $this->logger->info(
                '[CognitoAuthorization] Checking given store code',
                [
                    'providedStoreCode' => $currentStoreCode
                ]
            );
            $currentStore = $this->storeRepository->getActiveStoreByCode($currentStoreCode);
        } catch (\Exception) {
            $this->logger->warning(
                '[CognitoAuthorization] Falling back to english store, invalid store header or missing',
                [
                    'providedStoreCode' => $currentStoreCode
                ]
            );
            $currentStore = $this->storeRepository->getActiveStoreByCode('english');
        }

        try {
            $userData = $this->validateToken->validate(
                $token
            );

            try {
                $customer = $this->customerRepository->get(
                    $userData->email,
                    $currentStore->getWebsiteId()
                );

                $cognitoId = $customer->getCustomAttribute(
                    InstallCustomerCognitoIdAttribute::COGNITO_ID_ATTRIBUTE
                );
                if (!$cognitoId?->getValue() && $userData->sub) {
                    $customer->setCustomAttribute(
                        InstallCustomerCognitoIdAttribute::COGNITO_ID_ATTRIBUTE,
                        $userData->sub
                    );
                    $customer->setData('ignore_validation_flag', true);
                    $this->customerRepository->save($customer);
                }
            } catch (NoSuchEntityException) {
                $firstName = property_exists(
                    $userData,
                    'family_name'
                ) ? $userData->family_name : 'AnonymousF';
                $lastName = property_exists(
                    $userData,
                    'given_name'
                ) ? $userData->given_name : 'AnonymousL';
                $customerData = [
                    'firstname' => $firstName,
                    'email' => $userData->email,
                    'lastname' => $lastName,
                    'password' => $this->generateRandomPassword(),
                    'custom_attributes' => [
                        [
                            'value' => $userData->sub,
                            'attribute_code' => InstallCustomerCognitoIdAttribute::COGNITO_ID_ATTRIBUTE
                        ]
                    ]
                ];
                try {
                    $customer = $this->createCustomerAccount->execute(
                        $customerData,
                        $currentStore
                    );
                } catch (\Exception $e) {
                    $this->logger->warning(
                        '[CognitoAuthorization] Unable to create customer',
                        [
                            'message' => $e->getMessage(),
                        ]
                    );
                    throw new UserTokenException('Unable to finish decoding');
                }
            }

            $this->logger->info(
                '[CognitoAuthorization] Cognito token analysed successfully',
                [
                    'user' => $userData->email
                ]
            );

            $iat = \DateTimeImmutable::createFromFormat('U', (string) $userData->iat);
            $exp = \DateTimeImmutable::createFromFormat('U', (string) $userData->exp);

            return new UserToken(
                new JwtUserContext(
                    (int) $customer->getId(),
                    UserContextInterface::USER_TYPE_CUSTOMER
                ),
                new JwtTokenData($iat, $exp, new Header([]), new ClaimsPayload([]))
            );
        } catch (CognitoEncryptionKeyException $e) {
            $this->logger->warning(
                '[CognitoAuthorization] Token was unable to be decrypted, proceeding to normal magento flow',
                [
                    'message' => $e->getMessage()
                ]
            );

            $this->customerSession->logout();

            throw new UserTokenException('Invalid/Not cognito token');
        } catch (InvalidCognitoTokenException $e) {
            $this->logger->warning(
                '[CognitoAuthorization] Detected invalid token attempt, proceeding to normal magento flow',
                [
                    'message' => $e->getMessage()
                ]
            );

            $this->customerSession->logout();

            throw new UserTokenException('Invalid/Not cognito token');
        }
    }

    /**
     * @param int $length
     * @return string
     */
    private function generateRandomPassword(int $length = 12): string
    {
        $lowerCase = 'abcdefghijklmnopqrstuvwxyz';
        $upperCase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $digits = '0123456789';
        $specialChars = '!@#$%^&*()-_=+[]{}|;:,.<>?';

        $allChars = $lowerCase . $upperCase . $digits . $specialChars;
        $password = '';

        // Ensure at least three different character classes
        $password .= $lowerCase[rand(0, strlen($lowerCase) - 1)];
        $password .= $upperCase[rand(0, strlen($upperCase) - 1)];
        $password .= $digits[rand(0, strlen($digits) - 1)];
        $password .= $specialChars[rand(0, strlen($specialChars) - 1)];

        // Fill the rest of the password length with random characters
        for ($i = 4; $i < $length; $i++) {
            $password .= $allChars[rand(0, strlen($allChars) - 1)];
        }

        // Shuffle the password to prevent predictable patterns
        return str_shuffle($password);
    }
}
