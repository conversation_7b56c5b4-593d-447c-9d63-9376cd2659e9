<?php

declare(strict_types=1);

namespace Comave\RmaGraphQl\Model\Resolver;

use Comave\Rma\Service\EmailSender;
use Comave\Rma\Service\SellerReturnAddressProvider;
use Comave\SellerApi\Service\OrderSellersIdentifier;
use Magento\CustomerGraphQl\Model\Customer\GetCustomer;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\Uid;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Rma\Api\Data\RmaInterface;
use Magento\RmaGraphQl\Model\Formatter\Rma;
use Magento\RmaGraphQl\Model\Resolver\RequestReturn as CoreReturn;
use Magento\RmaGraphQl\Model\ResolverAccess;
use Magento\RmaGraphQl\Model\Rma\Comment;
use Magento\RmaGraphQl\Model\Rma\RequestRma;
use Magento\Sales\Api\OrderRepositoryInterface;
use Psr\Log\LoggerInterface;

class RequestReturn extends CoreReturn
{
    private array $sellerItems = [];

    /**
     * @param Comment $comment
     * @param Rma $rmaFormatter
     * @param ResolverAccess $resolverAccess
     * @param GetCustomer $getCustomer
     * @param Uid $idEncoder
     * @param RequestRma $requestRma
     * @param OrderRepositoryInterface $orderRepository
     * @param SellerReturnAddressProvider $sellerReturnAddressProvider
     * @param OrderSellersIdentifier $orderSellersIdentifier
     * @param EmailSender $emailSender
     * @param LoggerInterface $logger
     */
    public function __construct(
        Comment $comment,
        Rma $rmaFormatter,
        ResolverAccess $resolverAccess,
        GetCustomer $getCustomer,
        RequestRma $requestRma,
        protected Uid $idEncoder,
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly SellerReturnAddressProvider $sellerReturnAddressProvider,
        private readonly OrderSellersIdentifier $orderSellersIdentifier,
        private readonly EmailSender $emailSender,
        private readonly LoggerInterface $logger,
    ) {
        parent::__construct($comment, $rmaFormatter, $resolverAccess, $getCustomer, $idEncoder, $requestRma);
    }

    /**
     * @param Field $field
     * @param $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return array|\Magento\Framework\GraphQl\Query\Resolver\Value|mixed|void
     * @throws \Exception
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        $result = [];

        try {
            $this->checkSellerReturnAddresses(
                $args['input']['order_uid'] ?? '',
                $args['input']['items'] ?? []
            );
        } catch (\Exception $e) {
            $this->sellerItems = [];

            throw $e;
        }

        if (count($args['input']['items']) <= 1) {
            $result[] = parent::resolve($field, $context, $info, $value, $args);
        } else {
            foreach ($this->sellerItems as $itemsArr) {
                $args['input']['items'] = $itemsArr;
                $result[] = parent::resolve($field, $context, $info, $value, $args);
            }
        }

        foreach ($result as $rmaModel) {
            $this->sendSellerEmails($context, $rmaModel['model']);
        }

        $this->sellerItems = [];

        return $result;
    }

    /**
     * @param \Magento\Framework\GraphQl\Query\Resolver\ContextInterface $context
     * @param RmaInterface $rmaResult
     * @return void
     */
    private function sendSellerEmails(
        \Magento\Framework\GraphQl\Query\Resolver\ContextInterface $context,
        RmaInterface $rmaResult
    ): void {
        $websiteId = $context->getExtensionAttributes()?->getStore()->getWebsiteId() ?? 0;

        try {
            $this->emailSender->sendSellerEmail($rmaResult, $websiteId);
        } catch (\Exception $e) {
            $this->logger->warning(
                '[ComaveSellerRma] Unable to send new Seller RMA Email',
                [
                    'message' => $e->getMessage(),
                ]
            );
        }
    }

    /**
     * @param string $orderUid
     * @param array $items
     * @return void
     * @throws GraphQlInputException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function checkSellerReturnAddresses(
        string $orderUid,
        array $items
    ): void {
        $orderId = $this->idEncoder->decode($orderUid);
        $order = $this->orderRepository->get((int) $orderId);
        $sellerItems = $this->orderSellersIdentifier->get($order);

        if (empty($sellerItems)) {
            throw new GraphQlInputException(
                __('Unable to determine sellers for order %1', $order->getIncrementId())
            );
        }

        $sellerReturnAddresses = $this->sellerReturnAddressProvider->get($order);

        if (empty($sellerReturnAddresses)) {
            throw new GraphQlInputException(
                __('No return addresses found for current order')
            );
        }

        foreach ($items as $rmaItem) {
            $rmaOrderItemId = $this->idEncoder->decode($rmaItem['order_item_uid']);

            if (!isset($sellerReturnAddresses[$rmaOrderItemId])) {
                throw new GraphQlInputException(
                    __('No return address found for current item %1', $rmaItem['order_item_uid'])
                );
            }

            $sellerId = $sellerReturnAddresses[$rmaOrderItemId]['parent_id'];

            if (!isset($this->sellerItems[$sellerId])) {
                $this->sellerItems[$sellerId] = [];
            }

            $this->sellerItems[$sellerId][] = $rmaItem;
        }
    }
}
