<?php

declare(strict_types=1);

namespace Comave\RmaGraphQl\Plugin;

use Comave\Rma\Service\OrderGracePeriod;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\RmaGraphQl\Model\Resolver\CustomerOrder\Item\IsEligible;
use Magento\Sales\Api\Data\OrderItemInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Psr\Log\LoggerInterface;

class CheckIsEligible
{
    /**
     * @param OrderGracePeriod $orderGracePeriod
     * @param OrderRepositoryInterface $orderRepository
     * @param TimezoneInterface $dateTime
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly OrderGracePeriod $orderGracePeriod,
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly TimezoneInterface $dateTime,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * @param IsEligible $isEligibleResolver
     * @param bool $isEligibleForReturn
     * @param Field $field
     * @param $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @return bool
     */
    public function afterResolve(
        IsEligible $isEligibleResolver,
        bool $isEligibleForReturn,
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null
    ): bool {
        if (!isset($value['model']) && !($value['model'] instanceof OrderItemInterface)) {
            return $isEligibleForReturn;
        }

        if (isset($value['eligible_for_return']) && $value['eligible_for_return'] === false) {
            return false;
        }

        $orderItem = $value['model'];
        $order = $this->orderRepository->get(
            (int) $orderItem->getOrderId()
        );
        $currentDate = $this->dateTime->date()->setTime(0, 0);
        $gracePeriodDate = $this->dateTime->date(
            strtotime($order->getCreatedAt())
        )->setTime(0, 0)
            ->add(
                new \DateInterval(sprintf('P%dD', 14))
            );

        try {
            return $isEligibleForReturn && $this->orderGracePeriod->isAllowed(
                $order,
                $orderItem->getItemId()
            );
        } catch (\Exception $e) {
            $this->logger->warning(
                '[ComaveRmaEligibility] Failed checking grace period for RMA, falling back to 14 days from order creation',
                [
                    'message' => $e->getMessage(),
                    'order' => $order->getIncrementId()
                ]
            );

            return $isEligibleForReturn &&
                $currentDate->getTimestamp() <= $gracePeriodDate->getTimestamp();
        }
    }
}
