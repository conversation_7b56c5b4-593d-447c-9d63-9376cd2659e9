<?php
declare(strict_types=1);

namespace Comave\Marketplace\Setup\Patch\Data;

use Exception;
use Magento\Cms\Api\BlockRepositoryInterface;
use Magento\Cms\Api\GetBlockByIdentifierInterface;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Filesystem\Driver\File;
use Magento\Framework\Module\Dir;
use Magento\Framework\Module\Dir\Reader as DirReader;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Store\Api\StoreRepositoryInterface;
use Psr\Log\LoggerInterface;

use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\UrlInterface;

class UpdateWeltpixelFooterCmsBlockV2 implements DataPatchInterface
{
    private const CMS_BLOCK_WELTPIXEL_FOOTER = 'weltpixel_footer_v3';

    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly BlockRepositoryInterface $blockRepository,
        private readonly LoggerInterface $logger,
        private readonly GetBlockByIdentifierInterface $blockByIdentifier,
        private readonly StoreRepositoryInterface $storeRepository,
        private readonly Dir $dir,
        private readonly DirReader $dirReader,
        private readonly File $fileDriver,
        private readonly StoreManagerInterface $storeManager,
    ) {
    }

    /**
     * @return $this
     */
    public function apply(): void
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        try {
            $storeCode = 'english';
            $store = $this->storeRepository->get($storeCode);
            $storeId = (int)$store->getId();
            $cmsBlock = $this->blockByIdentifier->execute(
                self::CMS_BLOCK_WELTPIXEL_FOOTER,
                $storeId
            );

            $moduleEtcPath = $this->dir->getDir('Comave_Marketplace', Dir::MODULE_ETC_DIR);
            $htmlFilePath = $moduleEtcPath.DIRECTORY_SEPARATOR
                .'install-data'.DIRECTORY_SEPARATOR.'weltpixel_footer_v3.html';
            $htmlContent = $this->fileDriver->fileGetContents($htmlFilePath);

            $frontendBaseUrl = $this->getFrontendBaseUrl();

            // Replace placeholder
            $htmlContent = str_replace('{{frontend_base_url}}', $frontendBaseUrl, $htmlContent);
            $cmsBlock->setContent($htmlContent);

            $this->blockRepository->save($cmsBlock);
        } catch (Exception $exception) {
            $this->logger->error('The store does not exist.');
        } catch (NoSuchEntityException $e) {
            $this->logger->error("CMS block with identifier does not exist.".$e->getMessage());
        } catch (CouldNotSaveException $e) {
            $this->logger->error('Failed to save CMS block '.$e->getMessage());
        }

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * @return array
     */
    public static function getDependencies(): array
    {
        return [UpdateWeltpixelFooterCmsBlock::class];
    }

    /**
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * @return string
     */
    private function getFrontendBaseUrl(): string
    {
        return $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_WEB);
    }
}
