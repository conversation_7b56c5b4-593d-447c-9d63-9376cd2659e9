<?php

declare(strict_types=1);

/** @var \Magento\Sales\Model\Order $order */
$order = $block->getParentBlock()->getOrder();

/** @var \Comave\ShopifyOrderSync\ViewModel\ShopifyOrderInfo $viewModel */
$viewModel = $block->getViewModel();
$shopifyOrderId = $viewModel->getshopifyOrderId($order->getId());
?>

<?php //phpcs:disable SlevomatCodingStandard.ControlStructures.BlockControlStructureSpacing.IncorrectLinesCountBeforeControlStructure ?>
<?php if ($shopifyOrderId): ?>
    <tr>
        <th><?= $escaper->escapeHtml(__('Shopify Order ID')) ?></th>
        <td>
            <?php if ($viewModel->getShopifyOrderUrl($order)) : ?>
                <a href="<?= $escaper->escapeUrl($viewModel->getShopifyOrderUrl($order)) ?>" target="_blank">
                    <?= $escaper->escapeHtml($shopifyOrderId) ?>
                </a>
            <?php else: ?>
                <?= $escaper->escapeHtml($shopifyOrderId) ?>
            <?php endif; ?>
        </td>
    </tr>
<?php endif; ?>
